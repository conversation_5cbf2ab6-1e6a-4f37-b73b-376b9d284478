{
  /**
   * @file .vscode/settings.json
   *
   * @version 1.0.0
   * <AUTHOR>
   * @license MIT
   * @contributors
   *
   * @description
   * VS Code workspace settings for Velocita Optimized Wiki
   * Integrates with ESLint, Prettier, TypeScript, and Tailwind CSS
   * Optimized for Next.js 15 development with strict quality standards
   *
   * @since 1.0.0
   */

  // --------- EDITOR CORE SETTINGS
  "editor.fontSize": 14,
  "editor.fontFamily": "'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace",
  "editor.fontLigatures": true,
  "editor.lineHeight": 1.6,
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.renderWhitespace": "boundary",
  "editor.trimAutoWhitespace": true,
  "editor.wordWrap": "bounded",
  "editor.wordWrapColumn": 100,
  "editor.rulers": [100, 140],

  // --------- FORMATTING AND LINTING
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.formatOnType": false,
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit",
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit",
    "source.organizeImports": "explicit",
    "source.removeUnusedImports": "explicit",
    "source.sortImports": "explicit"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",

  // --------- FILE-SPECIFIC FORMATTERS
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.stylelint": "explicit"
    }
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.stylelint": "explicit"
    }
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.wordWrap": "bounded",
    "editor.wordWrapColumn": 100
  },

  // --------- TYPESCRIPT CONFIGURATION
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.suggest.autoImports": true,
  "typescript.suggest.includeCompletionsForModuleExports": true,
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.inlayHints.enumMemberValues.enabled": true,
  "typescript.inlayHints.functionLikeReturnTypes.enabled": true,
  "typescript.inlayHints.parameterNames.enabled": "all",
  "typescript.inlayHints.parameterTypes.enabled": true,
  "typescript.inlayHints.propertyDeclarationTypes.enabled": true,
  "typescript.inlayHints.variableTypes.enabled": true,
  "typescript.preferences.quoteStyle": "single",
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.workspaceSymbols.scope": "allOpenProjects",
  "typescript.format.semicolons": "insert",

  // --------- JAVASCRIPT CONFIGURATION
  "javascript.updateImportsOnFileMove.enabled": "always",
  "javascript.suggest.autoImports": true,
  "javascript.preferences.quoteStyle": "single",
  "javascript.format.semicolons": "insert",

  // --------- ESLINT CONFIGURATION
  "eslint.enable": true,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.format.enable": true,
  "eslint.codeAction.showDocumentation": {
    "enable": true
  },
  "eslint.run": "onType",
  "eslint.workingDirectories": ["."],

  // --------- PRETTIER CONFIGURATION
  "prettier.enable": true,
  "prettier.requireConfig": true,
  "prettier.useEditorConfig": false,
  "prettier.resolveGlobalModules": true,

  // --------- STYLELINT CONFIGURATION
  "stylelint.enable": true,
  "stylelint.validate": ["css", "scss", "sass"],
  "stylelint.snippet": ["css", "scss", "sass"],

  // --------- TAILWIND CSS CONFIGURATION
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
    ["cn\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
    ["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  "tailwindCSS.emmetCompletions": true,
  "tailwindCSS.suggestions": true,

  // --------- FILE ASSOCIATIONS
  "files.associations": {
    "*.css": "tailwindcss",
    "*.jsx": "javascriptreact",
    "*.tsx": "typescriptreact",
    "*.mjs": "javascript",
    "*.cjs": "javascript"
  },

  // --------- FILE SETTINGS
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  "files.trimTrailingWhitespace": true,
  "files.encoding": "utf8",
  "files.autoSave": "onFocusChange",

  // --------- SEARCH AND EXCLUDE SETTINGS
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/*.code-search": true,
    "**/.next": true,
    "**/out": true,
    "**/dist": true,
    "**/coverage": true,
    "**/.turbo": true,
    "**/build": true
  },
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/.next": true,
    "**/out": true,
    "**/dist": true,
    "**/.turbo": true,
    "**/coverage": true,
    "**/node_modules": false
  },
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/**": true,
    "**/.next/**": true,
    "**/out/**": true,
    "**/dist/**": true,
    "**/.turbo/**": true,
    "**/coverage/**": true
  },

  // --------- EXPLORER SETTINGS
  "explorer.sortOrder": "type",
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    "*.ts": "${capture}.js, ${capture}.d.ts.map, ${capture}.d.ts, ${capture}.js.map",
    "*.tsx": "${capture}.ts, ${capture}.js, ${capture}.jsx",
    "*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts",
    "*.jsx": "${capture}.js",
    "*.css": "${capture}.css.map, ${capture}.min.css",
    "*.scss": "${capture}.css, ${capture}.css.map",
    "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml, bun.lockb",
    "tailwind.config.js": "tailwind.config.ts, postcss.config.js, postcss.config.mjs",
    "next.config.js": "next.config.ts, next.config.mjs, next-env.d.ts",
    "tsconfig.json": "tsconfig.*.json",
    "eslint.config.js": "eslint.config.mjs, .eslintrc.*, .eslintignore",
    ".gitignore": ".gitattributes, .gitmodules, .gitmessage, .mailmap, .git-blame*",
    "README.md": "AUTHORS, BACKERS*, CHANGELOG*, CITATION*, CODE_OF_CONDUCT*, CONTRIBUTING*, CONTRIBUTORS, COPYING*, CREDITS, Changelog*, INSTALL*, LICENSE*, MAINTAINERS, Makefile, MIGRATION*, NOTICE, README*, SECURITY.md, SPONSORS*, changelog*"
  },

  // --------- BREADCRUMBS AND NAVIGATION
  "breadcrumbs.enabled": true,
  "breadcrumbs.symbolPath": "on",
  "breadcrumbs.symbolSortOrder": "type",

  // --------- MINIMAP AND SCROLLBAR
  "editor.minimap.enabled": true,
  "editor.minimap.maxColumn": 80,
  "editor.minimap.renderCharacters": false,
  "editor.scrollBeyondLastLine": false,

  // --------- INTELLISENSE AND SUGGESTIONS
  "editor.suggest.preview": true,
  "editor.suggest.showMethods": true,
  "editor.suggest.showFunctions": true,
  "editor.suggest.showConstructors": true,
  "editor.suggest.showFields": true,
  "editor.suggest.showVariables": true,
  "editor.suggest.showClasses": true,
  "editor.suggest.showStructs": true,
  "editor.suggest.showInterfaces": true,
  "editor.suggest.showModules": true,
  "editor.suggest.showProperties": true,
  "editor.suggest.showEvents": true,
  "editor.suggest.showOperators": true,
  "editor.suggest.showUnits": true,
  "editor.suggest.showValues": true,
  "editor.suggest.showConstants": true,
  "editor.suggest.showEnums": true,
  "editor.suggest.showEnumMembers": true,
  "editor.suggest.showKeywords": true,
  "editor.suggest.showWords": true,
  "editor.suggest.showColors": true,
  "editor.suggest.showFiles": true,
  "editor.suggest.showReferences": true,
  "editor.suggest.showCustomcolors": true,
  "editor.suggest.showFolders": true,
  "editor.suggest.showTypeParameters": true,
  "editor.suggest.showSnippets": true,
  "editor.suggest.showUsers": true,
  "editor.suggest.showIssues": true,

  // --------- GIT INTEGRATION
  "git.enableSmartCommit": true,
  "git.confirmSync": false,
  "git.autofetch": true,
  "git.autofetchPeriod": 180,

  // --------- EMMET CONFIGURATION
  "emmet.includeLanguages": {
    "javascript": "javascriptreact",
    "typescript": "typescriptreact"
  },
  "emmet.triggerExpansionOnTab": true,
  "emmet.showExpandedAbbreviation": "always",

  // --------- TERMINAL SETTINGS
  "terminal.integrated.fontSize": 13,
  "terminal.integrated.fontFamily": "'JetBrains Mono', 'Fira Code', monospace",
  "terminal.integrated.cursorBlinking": true,
  "terminal.integrated.cursorStyle": "line",

  // --------- PROBLEMS AND DIAGNOSTICS
  "problems.decorations.enabled": true,
  "problems.showCurrentInStatus": true,

  // --------- WORKBENCH APPEARANCE
  "workbench.iconTheme": "vscode-icons",
  "workbench.tree.indent": 12,
  "workbench.tree.renderIndentGuides": "always",

  // --------- EXTENSIONS SETTINGS
  "extensions.ignoreRecommendations": false,

  // --------- SECURITY SETTINGS
  "security.workspace.trust.untrustedFiles": "prompt",

  // --------- PERFORMANCE SETTINGS
  "typescript.disableAutomaticTypeAcquisition": false,
  "typescript.tsserver.maxTsServerMemory": 4096,

  // --------- DEBUG SETTINGS
  "debug.console.fontSize": 13,
  "debug.console.fontFamily": "'JetBrains Mono', 'Fira Code', monospace",

  // --------- BRACKET PAIR GUIDES & OUTLINE
  "editor.guides.bracketPairs": "active",
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairsHorizontal": "active",
  "cSpell.words": [
    "Bleck",
    "geist",
    "minecraft",
    "modpack",
    "morphism",
    "rgba",
    "tailwindcss",
    "turbopack",
    "velocita",
    "Velocita",
    "Xmark"
  ]
}
