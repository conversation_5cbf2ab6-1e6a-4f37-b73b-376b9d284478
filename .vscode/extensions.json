{
  /**
   * @file .vscode/extensions.json
   *
   * @version 1.0.0
   * <AUTHOR>
   * @license MIT
   * @contributors
   *
   * @description
   * VS Code workspace extension recommendations for Velocita Optimized Wiki
   * Curated list of essential extensions for Next.js 15 + TypeScript development
   * with focus on code quality, productivity, and modern web development
   *
   * @since 1.0.0
   */

  // --------- RECOMMENDED EXTENSIONS
  "recommendations": [
    // --------- CORE LANGUAGE SUPPORT
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",

    // --------- FORMATTING AND LINTING
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "stylelint.vscode-stylelint",
    "editorconfig.editorconfig",

    // --------- REACT AND NEXT.JS
    "ms-vscode.vscode-typescript-next",
    "burkeholland.simple-react-snippets",
    "dsznajder.es7-react-js-snippets",

    // --------- CODE QUALITY AND ANALYSIS
    "usernamehw.errorlens",
    "aaron-bond.better-comments",
    "streetsidesoftware.code-spell-checker",
    "waderyan.gitblame",

    // --------- PRODUCTIVITY
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "zignd.html-css-class-completion",
    "bradlc.vscode-tailwindcss",

    // --------- GIT AND VERSION CONTROL
    "eamodio.gitlens",
    "mhutchie.git-graph",
    "donjayamanne.githistory",

    // --------- VISUAL IMPROVEMENTS
    "vscode-icons-team.vscode-icons",
    "mechatroner.rainbow-csv",

    // --------- DEVELOPMENT UTILITIES
    "christian-kohler.npm-intellisense",
    "eg2.vscode-npm-script",
    "ms-vscode.vscode-npm-outdated",

    // --------- MARKDOWN AND DOCUMENTATION
    "yzhang.markdown-all-in-one",
    "davidanson.vscode-markdownlint",
    "bierner.markdown-preview-github-styles",

    // --------- DEBUGGING AND TESTING
    "ms-vscode.vscode-js-debug",
    "hbenl.vscode-test-explorer",

    // --------- TAILWIND CSS SPECIFIC
    "bradlc.vscode-tailwindcss",
    "heybourn.headwind",

    // --------- ADVANCED DEVELOPMENT
    "ms-vscode.remote-containers",
    "ms-vscode-remote.remote-ssh",
    "ms-vscode.remote-repositories",

    // --------- PERFORMANCE AND MONITORING
    "ms-vscode.vscode-bundle-analyzer",
    "ms-vscode.hexdump",

    // --------- ACCESSIBILITY
    "deque-systems.vscode-axe-linter"
  ],

  // --------- UNWANTED EXTENSIONS
  "unwantedRecommendations": [
    // Conflicting formatters
    "hookyqr.beautify",
    "ms-vscode.vscode-typescript",

    // Deprecated or conflicting linters
    "ms-vscode.vscode-eslint",
    "shinnn.stylelint",

    // Extensions that might interfere with our setup
    "formulahendry.code-runner",
    "ritwickdey.liveserver",

    // Auto-import extensions that might conflict
    "steoates.autoimport",
    "lukas-tr.materialdesignicons-intellisense"
  ]
}
