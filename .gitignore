# file: .gitignore
#
# version: 1.0.0
# author: <PERSON><PERSON>ckWolf25
# license: MIT
# contributors:
#
# description: This is a standardized gitignore file for a JavaScript/TypeScript + next.js with vercel deployment project.
#
# since 1.0.0

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env.development
.env.production
.env.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
