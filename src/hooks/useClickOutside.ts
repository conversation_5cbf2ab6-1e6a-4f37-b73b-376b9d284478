/**
 * @file hooks/useClickOutside.ts
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Hook to detect clicks outside an UI element.
 *
 * @since 1.0.0
 */

// --------- IMPORTS
import { useEffect } from 'react';

// --------- HOOK
/**
 * Hook to detect clicks outside the referenced element.
 * @param ref - React ref to the element
 * @param callback - Function to call on outside click
 */
export function useClickOutside<T extends HTMLElement>(
  ref: React.RefObject<T | null>,
  callback: () => void
) {
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        callback();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [ref, callback]);
}
