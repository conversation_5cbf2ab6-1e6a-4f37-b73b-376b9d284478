/**
 * @file hooks/useKeyboardNavigation.ts
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Hook for keyboard navigation in dropdowns.
 * Support more UI elements if needed for keyboard navigation.
 *
 * @since 1.0.0
 */

// --------- IMPORTS
import type { UseKeyboardNavigationResult } from '@/types/hooks';
import type { DropdownOption } from '@/types/ui';
import { useCallback, useEffect, useState } from 'react';

// --------- HOOK
/**
 * Hook for keyboard navigation in dropdowns.
 * @param options - Dropdown options
 * @param isOpen - Dropdown open state
 * @param onSelect - Callback for selecting option
 * @param onClose - Callback for closing dropdown
 */
export function useKeyboardNavigation(
  options: DropdownOption[],
  isOpen: boolean,
  onSelect: (option: DropdownOption) => void,
  onClose: () => void
): UseKeyboardNavigationResult {
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          setFocusedIndex(prev => {
            let next = prev + 1;
            while (next < options.length && options[next]?.disabled) next++;
            return next >= options.length ? 0 : next;
          });
          break;
        case 'ArrowUp':
          event.preventDefault();
          setFocusedIndex(prev => {
            let next = prev - 1;
            while (next >= 0 && options[next]?.disabled) next--;
            return next < 0 ? options.length - 1 : next;
          });
          break;
        case 'Enter':
        case ' ':
          event.preventDefault();
          if (
            focusedIndex >= 0 &&
            !options[focusedIndex]?.disabled &&
            options[focusedIndex]
          ) {
            onSelect(options[focusedIndex]!);
          }
          break;
        case 'Escape':
          event.preventDefault();
          onClose();
          break;
      }
    },
    [isOpen, options, focusedIndex, onSelect, onClose]
  );

  useEffect(() => {
    if (!isOpen) setFocusedIndex(-1);
  }, [isOpen]);

  return { focusedIndex, handleKeyDown };
}
