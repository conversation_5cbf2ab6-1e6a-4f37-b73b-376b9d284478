/**
 * @file layout.tsx
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Root layout component for the Velocita Optimized Wiki.
 * Server component that handles metadata and renders the basic HTML structure.
 * Client-side interactivity is delegated to ClientLayout component.
 *
 * @since 1.0.0
 */

// --------- IMPORTS
import ClientLayout from '@/components/ClientLayout';
import type { Metadata } from 'next';
import { <PERSON>eist, Geist_Mono } from 'next/font/google';
import './globals.css';

// --------- FONTS
const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

// --------- METADATA (Server Component can export this)
export const metadata: Metadata = {
  title: 'Velocita Optimized Wiki',
  description:
    'The official wiki for the Velocita Optimized Minecraft Modpack.',
  keywords: [
    'wiki',
    'minecraft',
    'modpack',
    'velocita optimized',
    'documentation',
    'help',
  ],
  openGraph: {
    title: 'Velocita Optimized Wiki',
    description:
      'The official wiki for the Velocita Optimized Minecraft Modpack.',
    url: 'https://github.com/BleckWolf25/vo-wiki',
    siteName: 'VO Wiki',
    type: 'website',
  },
};

// --------- MAIN LAYOUT (Server Component)
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} bg-background text-foreground overflow-x-hidden antialiased`}
      >
        <ClientLayout>{children}</ClientLayout>
      </body>
    </html>
  );
}
