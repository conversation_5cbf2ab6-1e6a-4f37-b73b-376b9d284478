/**
 * @file app/test-ui.tsx
 *
 * @description
 * Test page to showcase all UI & UX components for visual inspection and development.
 *
 * @since 1.0.0
 */

'use client';

import {
  Button,
  Card,
  Dropdown,
  ImageModal,
  LoadingSpinner,
  VideoPlayer,
} from '@/components/ui';
import Icon from '@/components/ui/Icon';
import MorphingDataVisualization from '@/components/ux/animations/MorphingDataVisualization';
import ParallaxStorytelling from '@/components/ux/animations/ParallaxStorytelling';
import ProgressScrollbar from '@/components/ux/animations/ProgressScrollbar';
import { useState } from 'react';

const dropdownOptions = [
  { label: 'Option 1', value: '1' },
  { label: 'Option 2', value: '2' },
  { label: 'Option 3', value: '3' },
];

export default function TestUIPage() {
  const [dropdownValue, setDropdownValue] = useState<string | number>('1');
  const [modalOpen, setModalOpen] = useState(false);

  return (
    <div className="bg-background text-foreground min-h-screen space-y-12 p-8">
      <h1 className="mb-8 text-3xl font-bold">UI & UX Components Showcase</h1>

      {/* Buttons */}
      <section>
        <h2 className="mb-4 text-xl font-semibold">Button Variants</h2>
        <div className="flex flex-wrap gap-4">
          <Button variant="primary">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="outline">Outline</Button>
          <Button variant="ghost">Ghost</Button>
          <Button variant="destructive">Destructive</Button>
          <Button variant="glass">Glass</Button>
          <Button variant="primary" loading>
            Loading
          </Button>
        </div>
      </section>

      {/* Card */}
      <section>
        <h2 className="mb-4 text-xl font-semibold">Card</h2>
        <Card header="Card Header" footer="Card Footer">
          <p>This is a card with glass morphism effect.</p>
        </Card>
      </section>

      {/* Dropdown */}
      <section>
        <h2 className="mb-4 text-xl font-semibold">Dropdown</h2>
        <Dropdown
          options={dropdownOptions}
          value={dropdownValue}
          onChange={val => setDropdownValue(val as string | number)}
          placeholder="Select an option"
          multiple={false}
        />
      </section>

      {/* Icon */}
      <section>
        <h2 className="mb-4 text-xl font-semibold">Icons</h2>
        <div className="flex items-center gap-6">
          <Icon name="home" size={32} />
          <Icon name="user" size={32} />
          <Icon name="beer" size={32} />
          <Icon name="search" size={32} />
          <Icon name="close" size={32} />
          <Icon name="loading" size={32} />
        </div>
      </section>

      {/* ImageModal */}
      <section>
        <h2 className="mb-4 text-xl font-semibold">Image Modal</h2>
        <Button variant="primary" onClick={() => setModalOpen(true)}>
          Open Image Modal
        </Button>
        <ImageModal
          src="/images/vo-logo.png"
          alt="VO Logo"
          open={modalOpen}
          onClose={() => setModalOpen(false)}
          caption="Velocita Optimized Logo"
        />
      </section>

      {/* LoadingSpinner */}
      <section>
        <h2 className="mb-4 text-xl font-semibold">Loading Spinner</h2>
        <LoadingSpinner size={48} />
      </section>

      {/* VideoPlayer */}
      <section>
        <h2 className="mb-4 text-xl font-semibold">Video Player</h2>
        <VideoPlayer
          src="/videos/sample.mp4"
          poster="/images/vo-logo.png"
          alt="Sample Video"
        />
      </section>

      {/* MorphingDataVisualization */}
      <section>
        <h2 className="mb-4 text-xl font-semibold">
          Morphing Data Visualization
        </h2>
        <MorphingDataVisualization
          config={{
            id: 'downloads-counter',
            type: 'counter',
            data: [{ value: 1234, label: 'Downloads' }],
            animationDuration: 2,
            easing: 'easeInOut',
          }}
        >
          {/* children required by MorphingDataVisualization */}
          <></>
        </MorphingDataVisualization>
      </section>

      {/* ParallaxStorytelling */}
      <section>
        <h2 className="mb-4 text-xl font-semibold">Parallax Storytelling</h2>
        <div className="h-[400px] overflow-hidden rounded-xl border">
          <ParallaxStorytelling
            sections={[
              {
                id: 'intro',
                title: 'Intro',
                content: <div className="p-8">Intro Section</div>,
                speed: 0.2,
                glassEffect: 'medium',
                height: 'auto',
              },
              {
                id: 'middle',
                title: 'Middle',
                content: <div className="p-8">Middle Section</div>,
                speed: 0.5,
                glassEffect: 'strong',
                height: 'auto',
              },
              {
                id: 'end',
                title: 'End',
                content: <div className="p-8">End Section</div>,
                speed: 0.8,
                glassEffect: 'light',
                height: 'auto',
              },
            ]}
          >
            {/* children required by ParallaxStorytelling */}
            <></>
          </ParallaxStorytelling>
        </div>
      </section>

      {/* ProgressScrollbar */}
      <section>
        <h2 className="mb-4 text-xl font-semibold">Progress Scrollbar</h2>
        <ProgressScrollbar
          config={{
            position: 'fixed-top',
            height: '8px',
            backgroundColor: 'rgba(30,41,59,0.2)',
            progressColor: '#38bdf8',
            glowEffect: true,
            smoothness: 0.2,
          }}
          showPercentage={true}
        />
      </section>
    </div>
  );
}
