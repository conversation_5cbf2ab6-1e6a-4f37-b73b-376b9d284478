/*
 * @file globals.css
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * This file contains global styles for the application.
 * It sets the default background and foreground colors, font families,
 * and includes styles for dark mode.
 * It includes custom CSS variables and imports Tailwind CSS.
 *
 * @since 1.0.0
 */

/* =============================================
IMPORTS
================================================ */
@import "tailwindcss";

/* =============================================
VARIABLES
================================================ */
:root {
  --background: #10131a;
  --foreground: #e5e7eb;
  --font-geist-sans: "Geist", system-ui, sans-serif;
  --font-geist-mono: "Geist Mono", monospace;
  /* Glass effect colors */
  --glass: rgba(30, 41, 59, 0.1);
  --glass-light: rgba(30, 41, 59, 0.05);
  --glass-strong: rgba(30, 41, 59, 0.2);
  --accent: #1e293b;
  --accent-light: #334155;
  --accent-dark: #0f172a;
  --primary: #38bdf8;
  --primary-dark: #0ea5e9;
  --primary-light: #7dd3fc;
  --muted: #64748b;
  --border: #22232b;
}

/* =============================================
MEDIA QUERIES
================================================ */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0d13;
    --foreground: #f1f5f9;
  }
}

/* =============================================
BODY STYLES
================================================ */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

/* =============================================
LIQUID GLASS EFFECTS
================================================ */
.liquid-glass {
  background: var(--glass);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: 1.25rem;
  box-shadow: 0 4px 32px 0 var(--glass);
  border: 1px solid var(--border);
}
.liquid-glass-light {
  background: var(--glass-light);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-radius: 1.25rem;
  box-shadow: 0 2px 16px 0 var(--glass-light);
  border: 1px solid var(--border);
}
.liquid-glass-strong {
  background: var(--glass-strong);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border-radius: 1.5rem;
  box-shadow: 0 8px 40px 0 var(--glass-strong);
  border: 1.5px solid var(--primary);
}

/* =============================================
GLASS SHIMMER EFFECTS & ANIMATIONS
================================================ */

/* Shimmer animation for glass */
@keyframes glass-shimmer {
  0%,
  100% {
    background-position: 200% 0;
    opacity: 0.3;
  }
  50% {
    background-position: -200% 0;
    opacity: 0.6;
  }
}
.glass-shimmer {
  background-image: linear-gradient(90deg, transparent, #334155, transparent);
  background-size: 200% 100%;
  animation: glass-shimmer 16s ease-in-out infinite;
}
