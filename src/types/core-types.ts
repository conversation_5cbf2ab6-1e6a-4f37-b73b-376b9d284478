/**
 * @file core-types.ts
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Centralized core types for the Velocita Optimized Wiki layout (server and client).
 * These types are shared between layout components to avoid hardcoding and duplication.
 *
 * @since 1.0.0
 */

// --------- TYPES
/**
 * Navigation link configuration
 */
export interface NavLink {
  /** Unique identifier for the link */
  id: string;

  /** Display text for the link */
  label: string;

  /** URL or route path */
  href: string;

  /** Icon name from centralized Icons component */
  icon?: string;

  /** Whether this link is external */
  external?: boolean;

  /** Accessibility label override */
  ariaLabel?: string;

  /** Whether link should open in new tab */
  newTab?: boolean;
}

/**
 * Navbar configuration and behavior
 */
export interface NavbarConfig {
  /** Brand/logo configuration */
  brand: {
    /** Brand name/title */
    name: string;

    /** Logo icon name */
    icon?: string;

    /** Home page URL */
    href: string;

    /** Custom logo image URL */
    logoUrl?: string;
  };
  /** Navigation links */
  links: NavLink[];

  /** Whether navbar should be sticky */
  sticky?: boolean;

  /** Show mobile menu toggle */
  showMobileMenu?: boolean;

  /** Custom CSS classes */
  className?: string;

  /** Whether to show search functionality */
  showSearch?: boolean;

  /** Custom search placeholder */
  searchPlaceholder?: string;
}

/**
 * Footer section configuration
 */
export interface FooterSection {
  /** Section title */
  title: string;

  /** Section links */
  links: NavLink[];

  /** Section icon */
  icon?: string;
}

/**
 * Social media link configuration
 */
export interface SocialLink {
  /** Platform name */
  platform: string;

  /** Platform URL */
  url: string;

  /** Icon name from centralized Icons component */
  icon: string;

  /** Platform display name */
  label: string;

  /** Custom aria label */
  ariaLabel?: string;
}

/**
 * Footer configuration
 */
export interface FooterConfig {
  /** Footer brand information */
  brand: {
    /** Brand name */
    name: string;

    /** Brand description */
    description?: string;

    /** Brand icon */
    icon?: string;
  };
  /** Footer sections */
  sections: FooterSection[];

  /** Social media links */
  social: SocialLink[];

  /** Copyright information */
  copyright: {
    /** Copyright text */
    text: string;

    /** Copyright year */
    year: number;

    /** Copyright holder */
    holder: string;
  };
  /** Legal links */
  legal?: NavLink[];

  /** Custom CSS classes */
  className?: string;
}

/**
 * Liquid glass effect configuration
 */
export interface LiquidGlassConfig {
  /** Glass opacity level */
  opacity?: 'light' | 'medium' | 'strong';

  /** Blur intensity */
  blur?: 'sm' | 'md' | 'lg';

  /** Glow effect enabled */
  glow?: boolean;

  /** Shimmer animation enabled */
  shimmer?: boolean;

  /** Border style */
  border?: 'subtle' | 'accent' | 'primary';
}

/**
 * Component state for interactive elements
 */
export interface ComponentState {
  /** Whether component is currently hovered */
  isHovered?: boolean;

  /** Whether component is currently focused */
  isFocused?: boolean;

  /** Whether component is currently active/pressed */
  isActive?: boolean;

  /** Whether component is currently loading */
  isLoading?: boolean;

  /** Whether component is disabled */
  isDisabled?: boolean;
}

/**
 * Responsive breakpoint configuration
 */
export interface ResponsiveConfig {
  /** Mobile breakpoint (default: 768px) */
  mobile?: number;

  /** Tablet breakpoint (default: 1024px) */
  tablet?: number;

  /** Desktop breakpoint (default: 1280px) */
  desktop?: number;

  /** Large desktop breakpoint (default: 1536px) */
  xl?: number;
}

/**
 * Animation configuration for components
 */
export interface AnimationConfig {
  /** Animation duration in seconds */
  duration?: number;

  /** Animation easing function */
  easing?: string;

  /** Animation delay in seconds */
  delay?: number;

  /** Whether animation should repeat */
  repeat?: boolean | number;

  /** Animation direction */
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
}

/**
 * Accessibility configuration
 */
export interface AccessibilityConfig {
  /** ARIA label */
  ariaLabel?: string;

  /** ARIA description */
  ariaDescription?: string;

  /** ARIA expanded state */
  ariaExpanded?: boolean;

  /** ARIA controls attribute */
  ariaControls?: string;

  /** Tab index */
  tabIndex?: number;

  /** Role attribute */
  role?: string;

  /** Whether element should be announced by screen readers */
  ariaLive?: 'off' | 'polite' | 'assertive';
}
