/**
 * @file src/types/hooks.ts
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Type definitions for hooks in the Velocita Optimized Wiki.
 * This file defines the types for:
 *  - UseKeyboardNavigationResult
 * These types are used to ensure type safety.
 */

// --------- TYPES
// Use keyboard Navigation Result
export interface UseKeyboardNavigationResult {
  focusedIndex: number;
  handleKeyDown: (event: React.KeyboardEvent) => void;
}

/**
 * Scroll position hook return type
 */
export interface UseScrollPositionReturn {
  scrollY: number;
  scrollDirection: 'up' | 'down';
  scrollProgress: number;
  isScrolling: boolean;
}

/**
 * Intersection observer hook return type
 */
export interface UseIntersectionObserverReturn {
  isIntersecting: boolean;
  intersectionRatio: number;
  entry: IntersectionObserverEntry | null;
}

/**
 * Parallax effect hook return type
 */
export interface UseParallaxReturn {
  transform: string;
  opacity: number;
  scale: number;
}

/**
 * Morphing animation hook return type
 */
export interface UseMorphingAnimationReturn {
  currentStep: number;
  progress: number;
  isAnimating: boolean;
  nextStep: () => void;
  previousStep: () => void;
  jumpToStep: (step: number) => void;
}
