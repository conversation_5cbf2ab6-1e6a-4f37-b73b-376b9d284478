declare module 'friendly-date' {
  export type TimeFormat = '12h' | '24h';
  export type MaxUnit =
    | 'second'
    | 'minute'
    | 'hour'
    | 'day'
    | 'week'
    | 'month'
    | 'year';

  export interface LocaleConfig {
    units: Record<string, string>;
    unitsPlural: Record<string, string>;
    relative: Record<string, string>;
    days: { short: string[]; long: string[] };
    months: { short: string[]; long: string[] };
  }

  export interface FormatOptions {
    locale?: LocaleConfig;
    includeTime?: boolean;
    timeFormat?: TimeFormat;
    maxUnit?: MaxUnit;
    justNowThreshold?: number;
    useWords?: boolean;
  }

  function format(
    date: Date | string | number,
    referenceDate?: Date | string | number,
    options?: FormatOptions
  ): string;

  export default format;
  export { format, FormatOptions, LocaleConfig };
}
