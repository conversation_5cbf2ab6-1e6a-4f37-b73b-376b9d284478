/**
 * @file types/ux.ts
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Centralized types for UX animations, transitions, and UI components.
 * Supports liquid glass effects and modern animation patterns.
 *
 * @since 1.0.0
 */

// --------- IMPORTS
import type { MotionProps } from 'framer-motion';

// --------- ANIMATION TYPES
/**
 * Common animation variants for consistent motion design
 */
export interface AnimationVariants {
  initial: Record<string, any>;
  animate: Record<string, any>;
  exit?: Record<string, any>;
  hover?: Record<string, any>;
  tap?: Record<string, any>;
}

/**
 * Parallax section configuration
 */
export interface ParallaxSection {
  id: string;
  title: string;
  content: React.ReactNode;
  backgroundImage?: string;
  speed: number; // Multiplier for parallax effect (0.1 to 2.0)
  glassEffect?: 'light' | 'medium' | 'strong';
  height?: 'auto' | 'screen' | 'half-screen';
}

/**
 * Data visualization morphing configuration
 */
export interface MorphingDataConfig {
  id: string;
  type: 'chart' | 'counter' | 'progress' | 'stats';
  data: any[];
  animationDuration: number;
  easing: string;
  morphTrigger?: 'scroll' | 'hover' | 'click' | 'auto';
}

/**
 * Scroll narrative step configuration
 */
export interface ScrollNarrativeStep {
  id: string;
  triggerAt: number; // Percentage of viewport (0-100)
  title: string;
  description: string;
  visual?: React.ReactNode;
  animation: AnimationVariants;
  duration: number;
}

/**
 * Progress scroll bar configuration
 */
export interface ProgressScrollConfig {
  height: string;
  backgroundColor: string;
  progressColor: string;
  glowEffect: boolean;
  position: 'top' | 'bottom' | 'fixed-top';
  smoothness: number;
}

// --------- COMPONENT PROPS
/**
 * Base animation component props
 */
export interface BaseAnimationProps extends MotionProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  duration?: number;
  easing?: string;
}

/**
 * Parallax storytelling component props
 */
export interface ParallaxStorytellingProps extends BaseAnimationProps {
  sections: ParallaxSection[];
  containerClassName?: string;
  enableGlassEffects?: boolean;
  reducedMotion?: boolean;
}

/**
 * Morphing data visualization props
 */
export interface MorphingDataVisualizationProps extends BaseAnimationProps {
  config: MorphingDataConfig;
  onMorphComplete?: () => void;
  interactive?: boolean;
  theme?: 'dark' | 'light' | 'glass';
}

/**
 * Scroll-triggered narrative props
 */
export interface ScrollTriggeredNarrativeProps extends BaseAnimationProps {
  steps: ScrollNarrativeStep[];
  containerHeight?: string;
  progressIndicator?: boolean;
  autoProgress?: boolean;
  progressSpeed?: number;
}

/**
 * Progress scroll bar props
 */
export interface ProgressScrollBarProps {
  config: ProgressScrollConfig;
  showPercentage?: boolean;
  onProgressChange?: (progress: number) => void;
}

/**
 * Page fade transition props
 */
export interface PageFadeTransitionProps extends BaseAnimationProps {
  mode: 'fade' | 'slide' | 'scale' | 'liquid-morph';
  direction?: 'up' | 'down' | 'left' | 'right';
  overlay?: boolean;
  overlayColor?: string;
}

// --------- ANIMATION CONFIGURATION TYPES
/**
 * Liquid glass animation configuration
 */
export interface LiquidGlassConfig {
  intensity: 'subtle' | 'medium' | 'strong';
  shimmer: boolean;
  float: boolean;
  pulseGlow: boolean;
  blurAmount: number;
  borderRadius: string;
}

/**
 * Performance configuration for animations
 */
export interface AnimationPerformanceConfig {
  enableReducedMotion: boolean;
  enableHardwareAcceleration: boolean;
  maxConcurrentAnimations: number;
  debounceScrollEvents: number;
  enableGPUOptimization: boolean;
}

/**
 * Accessibility configuration for animations
 */
export interface AnimationA11yConfig {
  respectPrefersReducedMotion: boolean;
  provideFocusIndicators: boolean;
  enableKeyboardNavigation: boolean;
  announceChanges: boolean;
  skipAnimationsOnTabNavigation: boolean;
}

// --------- UTILITY TYPES
/**
 * Animation timing function presets
 */
export type AnimationEasing =
  | 'linear'
  | 'easeIn'
  | 'easeOut'
  | 'easeInOut'
  | 'circIn'
  | 'circOut'
  | 'circInOut'
  | 'backIn'
  | 'backOut'
  | 'backInOut'
  | 'anticipate'
  | 'bounceIn'
  | 'bounceOut'
  | 'bounceInOut';

/**
 * Responsive breakpoints for animations
 */
export interface ResponsiveAnimationConfig {
  mobile: Partial<AnimationVariants>;
  tablet: Partial<AnimationVariants>;
  desktop: Partial<AnimationVariants>;
  ultrawide?: Partial<AnimationVariants>;
}

/**
 * Theme-aware animation configuration
 */
export interface ThemeAnimationConfig {
  light: Partial<AnimationVariants>;
  dark: Partial<AnimationVariants>;
  glass: Partial<AnimationVariants>;
}

// --------- ADVANCED UX TYPES

/**
 * Complex animation orchestration
 */
export interface AnimationOrchestration {
  id: string;
  name: string;
  sequence: Array<{
    element: string;
    animation: AnimationVariants;
    delay: number;
    duration: number;
  }>;
  loop?: boolean;
  autoStart?: boolean;
  triggers?: Array<'scroll' | 'hover' | 'click' | 'focus' | 'visible'>;
}

/**
 * Dynamic animation system configuration
 */
export interface DynamicAnimationSystem {
  globalConfig: AnimationPerformanceConfig;
  a11yConfig: AnimationA11yConfig;
  themeConfig: ThemeAnimationConfig;
  responsiveConfig: ResponsiveAnimationConfig;
  orchestrations: AnimationOrchestration[];
}

// --------- RE-EXPORTS
export type { MotionProps, Transition, Variants } from 'framer-motion';
