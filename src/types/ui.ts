/**
 * @file src/types/ui.ts
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Type definitions for UI components in the Velocita Optimized Wiki.
 * This file defines the types for:
 *  - Button component
 *  - Dropdown component
 *  - Card component
 *  - Image Modal component
 *  - Video Player component
 *  - Icon component
 * These types are used to ensure type safety and proper prop validation
 */

// --------- IMPORTS
import type { ButtonHTMLAttributes, ReactNode } from 'react';

// --------- BUTTON TYPES
/**
 * Props for the Button component.
 * @template T - HTML element type, defaults to button.
 */
export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  /**
   * Visual style variant of the button.
   * @default 'primary'
   * - 'glass': Glass morphism style (translucent, blurred background)
   */
  variant?:
    | 'primary'
    | 'secondary'
    | 'outline'
    | 'ghost'
    | 'destructive'
    | 'glass';

  /**
   * Size of the button.
   * @default 'md'
   */
  size?: 'sm' | 'md' | 'lg' | 'xl';

  /**
   * If true, shows a loading spinner and disables the button.
   * @default false
   */
  loading?: boolean;

  /**
   * Custom loading indicator (replaces default spinner) when loading is true.
   */
  loadingIndicator?: React.ReactNode;

  /**
   * Icon to display on the left side of the button.
   */
  leftIcon?: React.ReactNode;

  /**
   * Icon to display on the right side of the button.
   */
  rightIcon?: React.ReactNode;

  /**
   * If true, button takes full width of its container.
   * @default false
   */
  fullWidth?: boolean;

  /**
   * Button content.
   */
  children: React.ReactNode;

  /**
   * Accessible label for the button (for screen readers). Overrides visible text if provided.
   */
  'aria-label'?: string;

  /**
   * ARIA role for the button.
   */
  role?: string;

  /**
   * Pass any additional ARIA or accessibility attributes.
   */
  [ariaAttr: `aria-${string}`]: any;
}

// --------- DROPDOWN TYPES
/**
 * Option for Dropdown components.
 */
export interface DropdownOption {
  /** Unique value for the option. */
  value: string | number;

  /** Display label for the option. */
  label: string;

  /** If true, disables this option. */
  disabled?: boolean;

  /** Optional description for the option. */
  description?: string;

  /** Optional icon for the option. */
  icon?: React.ReactNode;
}

/**
 * Base props for Dropdown components.
 * @template T - Option value type (string | number by default)
 */
export interface BaseDropdownProps {
  /** List of options to display. */
  options: DropdownOption[];

  /** Placeholder text when no selection is made. */
  placeholder?: string;

  /** If true, disables the dropdown. @default false */
  disabled?: boolean;

  /** If true, enables search input. @default false */
  searchable?: boolean;

  /** If true, allows clearing the selection(s). @default false */
  clearable?: boolean;

  /** Placeholder for the search input. */
  searchPlaceholder?: string;

  /** If true or string, shows error state. */
  error?: boolean | string;

  /** Error message to display. */
  errorMessage?: string;

  /** Help text to display below the dropdown. */
  helpText?: string;

  /** Label for the dropdown. */
  label?: string;

  /** If true, visually hides the label. @default false */
  hideLabel?: boolean;

  /** Additional class names for the dropdown container. */
  className?: string;

  /** Max height for the dropdown menu. @default '256px' */
  maxHeight?: number | string;

  /** If true, shows loading spinner. @default false */
  loading?: boolean;

  /** Message to display when no options are available. */
  emptyMessage?: string;

  /**
   * Custom render function for dropdown options.
   * Receives the option, selected state, and focus state.
   */
  renderOption?: (
    option: DropdownOption,
    selected: boolean,
    focused: boolean
  ) => React.ReactNode;
}

/**
 * Props for single-select Dropdown.
 *
 * Controlled: Provide value/onChange. Uncontrolled: Omit value/onChange.
 * If clearable, onChange may be called with undefined when cleared.
 */
export interface SingleSelectProps extends BaseDropdownProps {
  /**
   * If true, enables multi-select mode. Should be omitted or false for single-select.
   * @default false
   */
  multiple?: false;

  /**
   * Selected value. Controlled usage: provide value and onChange. Uncontrolled: omit both.
   */
  value?: string | number;

  /**
   * Callback when selection changes. If clearable, may be called with undefined.
   */
  onChange?: (value: string | number | undefined) => void;
}

/**
 * Props for multi-select Dropdown.
 *
 * Controlled: Provide value/onChange. Uncontrolled: Omit value/onChange.
 */
export interface MultiSelectProps extends BaseDropdownProps {
  /**
   * If true, enables multi-select mode.
   */
  multiple: true;

  /**
   * Array of selected values. Controlled usage: provide value and onChange. Uncontrolled: omit both.
   */
  value?: Array<string | number>;

  /**
   * Callback when selection changes.
   */
  onChange?: (values: Array<string | number>) => void;

  /**
   * Maximum number of selections allowed.
   */
  maxSelections?: number;

  /**
   * If true, shows a count of selected items instead of tags.
   * @default false
   */
  showCount?: boolean;
}

/**
 * Props for Dropdown component (single or multi-select).
 */
export type DropdownProps = SingleSelectProps | MultiSelectProps;

// --------- CARD TYPES
/**
 * Props for Card component.
 * @template T - HTML element type for the card root (default: div)
 *
 * Supported elements for `as`: 'div', 'section', 'article', 'aside', 'main', etc.
 *
 * header/footer can be ReactNode or a function returning ReactNode for advanced rendering.
 */
export interface CardProps<T extends React.ElementType = 'div'> {
  /** Card content. */
  children: ReactNode;

  /** Additional class names for the card. */
  className?: string;

  /** Optional header content or render function. */
  header?: ReactNode | (() => ReactNode);

  /** Optional footer content or render function. */
  footer?: ReactNode | (() => ReactNode);

  /**
   * Custom tag for the card root element (e.g., 'section', 'article').
   * @default 'div'
   */
  as?: T;
}

// --------- IMAGE MODAL TYPES
/**
 * Props for ImageModal component.
 */
export interface ImageModalProps {
  /** Image source URL. */
  src: string;

  /** Image alt text. */
  alt?: string;

  /** If true, modal is open. */
  open: boolean;

  /** Callback to close the modal. */
  onClose: () => void;

  /** Optional caption for the image. */
  caption?: string;

  /**
   * ARIA role for the modal/dialog.
   */
  role?: string;

  /**
   * Tab index for accessibility.
   */
  tabIndex?: number;

  /**
   * Additional ARIA/accessibility attributes.
   */
  [ariaAttr: `aria-${string}`]: any;

  /**
   * Called when the modal is opened.
   */
  onOpen?: () => void;

  /**
   * Called when an error occurs loading the image.
   */
  onError?: (event: React.SyntheticEvent<HTMLImageElement, Event>) => void;

  /**
   * Maximum width for responsive modal.
   */
  maxWidth?: number | string;

  /**
   * Aspect ratio for the image/modal.
   */
  aspectRatio?: string;
}

// --------- VIDEO PLAYER TYPES
/**
 * Props for VideoPlayer component.
 */
export interface VideoPlayerProps {
  /** Video source URL. */
  src: string;

  /** Poster image URL. */
  poster?: string;

  /** Alt text for the video. */
  alt?: string;

  /** Additional class names for the video player. */
  className?: string;

  /**
   * ARIA role for the video player.
   */
  role?: string;

  /**
   * Tab index for accessibility.
   */
  tabIndex?: number;

  /**
   * Additional ARIA/accessibility attributes.
   */
  [ariaAttr: `aria-${string}`]: any;

  /**
   * Called when the video is opened/played.
   */
  onOpen?: () => void;

  /**
   * Called when an error occurs loading the video.
   */
  onError?: (event: React.SyntheticEvent<HTMLVideoElement, Event>) => void;

  /**
   * Maximum width for responsive video.
   */
  maxWidth?: number | string;

  /**
   * Aspect ratio for the video.
   */
  aspectRatio?: string;
}

// --------- ICON TYPES
/**
 * IconName: Union of all available icon names in the ICONS map.
 */
export type IconName =
  | 'beer'
  | 'user'
  | 'home'
  | 'search'
  | 'close'
  | 'loading'
  | 'book'
  | 'sparkles'
  | 'github'
  | 'discord'
  | 'arrowUp';

/**
 * Props for the Icon component.
 */
export interface IconProps {
  /** Name of the icon to render (must exist in ICONS map). */
  name: IconName;

  /** Icon size (number or string). Default: 24 */
  size?: number | string;

  /** Icon color. Default: 'currentColor' */
  color?: string;

  /** Additional class names for the icon wrapper. */
  className?: string;

  /** Accessible title for the icon. */
  title?: string;

  /** Accessible label for screen readers. */
  'aria-label'?: string;

  /** Additional props passed to the icon component. */
  [key: string]: any;
}

// --------- LOADING SPINNER TYPES
/**
 * Props for LoadingSpinner component.
 */
export interface LoadingSpinnerProps {
  /** Size of the spinner (number in px or CSS string). Default: 24 */
  size?: number | string;

  /** Color of the spinner. Default: 'currentColor' */
  color?: string;

  /** Additional class names for the spinner. */
  className?: string;

  /** Accessible label for screen readers. */
  'aria-label'?: string;

  /** ARIA role for the spinner. Default: 'status' */
  role?: string;

  /** Additional ARIA/accessibility attributes. */
  [ariaAttr: `aria-${string}`]: any;
}
