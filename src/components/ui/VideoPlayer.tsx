/**
 * @file components/ui/VideoPlayer.tsx
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Reusable Video Player component with glass morphism styling.
 * Supports play/pause, mute/unmute, and progress tracking.
 * Fully accessible with keyboard navigation and ARIA attributes.
 *
 * @since 1.0.0
 */

'use client';

// --------- IMPORTS
import { cn } from '@/lib/utils';
import type { VideoPlayerProps } from '@/types/ui';
import React, { useRef, useState } from 'react';

// --------- COMPONENT
const VideoPlayer: React.FC<VideoPlayerProps> = ({
  src,
  poster,
  alt = 'Video',
  className,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [progress, setProgress] = useState(0);

  const handlePlayPause = () => {
    if (!videoRef.current) return;
    if (videoRef.current.paused) {
      videoRef.current.play();
      setIsPlaying(true);
    } else {
      videoRef.current.pause();
      setIsPlaying(false);
    }
  };

  const handleMute = () => {
    if (!videoRef.current) return;
    videoRef.current.muted = !videoRef.current.muted;
    setIsMuted(videoRef.current.muted);
  };

  const handleProgress = () => {
    if (!videoRef.current) return;
    const percent =
      (videoRef.current.currentTime / videoRef.current.duration) * 100;
    setProgress(percent);
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!videoRef.current) return;
    const value = Number(e.target.value);
    videoRef.current.currentTime = (value / 100) * videoRef.current.duration;
    setProgress(value);
  };

  return (
    <div
      className={cn(
        'shadow-glass-lg bg-glass-strong border-border/50 relative w-full max-w-2xl overflow-hidden rounded-2xl border backdrop-blur-xl',
        className
      )}
    >
      <video
        ref={videoRef}
        src={src}
        poster={poster}
        className="h-auto w-full bg-black"
        tabIndex={0}
        aria-label={alt}
        onTimeUpdate={handleProgress}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
      />
      {/* Controls Overlay */}
      <div className="bg-glass-gradient absolute right-0 bottom-0 left-0 flex flex-col gap-2 p-4 backdrop-blur-md">
        {/* Progress Bar */}
        <input
          type="range"
          min={0}
          max={100}
          value={progress}
          onChange={handleSeek}
          className="accent-primary h-1 w-full cursor-pointer"
          aria-label="Seek video"
        />
        <div className="flex items-center gap-3">
          {/* Play/Pause */}
          <button
            onClick={handlePlayPause}
            className="bg-glass-light hover:bg-glass-strong focus-visible:ring-primary rounded-lg p-2 focus-visible:ring-2"
            aria-label={isPlaying ? 'Pause video' : 'Play video'}
          >
            {isPlaying ? (
              <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                <rect
                  x="5"
                  y="4"
                  width="3"
                  height="12"
                  rx="1.5"
                  fill="currentColor"
                />
                <rect
                  x="12"
                  y="4"
                  width="3"
                  height="12"
                  rx="1.5"
                  fill="currentColor"
                />
              </svg>
            ) : (
              <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                <path d="M6 4l10 6-10 6V4z" fill="currentColor" />
              </svg>
            )}
          </button>
          {/* Mute/Unmute */}
          <button
            onClick={handleMute}
            className="bg-glass-light hover:bg-glass-strong focus-visible:ring-primary rounded-lg p-2 focus-visible:ring-2"
            aria-label={isMuted ? 'Unmute video' : 'Mute video'}
          >
            {isMuted ? (
              <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                <path d="M3 8v4h4l5 5V3L7 8H3z" fill="currentColor" />
                <line
                  x1="16"
                  y1="6"
                  x2="6"
                  y2="16"
                  stroke="currentColor"
                  strokeWidth="2"
                />
              </svg>
            ) : (
              <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                <path d="M3 8v4h4l5 5V3L7 8H3z" fill="currentColor" />
              </svg>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

// --------- COMPONENT PROPS
VideoPlayer.displayName = 'VideoPlayer';

// --------- EXPORTS
export default VideoPlayer;
export { VideoPlayer };
