/**
 * @file components/ui/Card.tsx
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Reusable glass morphism (liquid glass) Card component for VO-Wiki UI.
 * Supports optional header and footer content.
 *
 * @since 1.0.0
 */

'use client';

// --------- IMPORTS
import { cn } from '@/lib/utils';
import type { CardProps } from '@/types/ui';
import React from 'react';

// --------- COMPONENT
/**
 * Card component with glass morphism effect.
 *
 * @template T - HTML element type for the card root (default: div)
 * @param {CardProps<T>} props - Card props
 * @param {React.ReactNode} props.children - Card content
 * @param {string} [props.className] - Additional class names
 * @param {React.ReactNode|() => React.ReactNode} [props.header] - Optional header content or render function
 * @param {React.ReactNode|() => React.ReactNode} [props.footer] - Optional footer content or render function
 * @param {T} [props.as] - Custom tag for the card root element (e.g., 'section', 'article'). Defaults to 'div'.
 */
const Card = <T extends React.ElementType = 'div'>({
  children,
  className,
  header,
  footer,
  as,
  ...rest
}: CardProps<T> & React.ComponentPropsWithoutRef<T>) => {
  const Tag = as || 'div';
  return (
    <Tag
      className={cn(
        `border-border/50 bg-glass-strong shadow-glass-lg hover:shadow-glass-xl relative w-full overflow-hidden rounded-3xl border backdrop-blur-xl transition-shadow duration-300`,
        className
      )}
      {...rest}
    >
      {/* Glass reflection overlay */}
      <div className="absolute inset-0 transition-opacity duration-300 opacity-0 pointer-events-none select-none bg-gradient-to-br from-white/10 via-transparent to-transparent hover:opacity-100" />
      {header && (
        <div className="px-6 pt-6 pb-2 text-lg font-semibold text-foreground/90">
          {typeof header === 'function' ? header() : header}
        </div>
      )}
      <div className={cn(['px-6 py-4', header ? 'pt-2' : ''])}>{children}</div>
      {footer && (
        <div className="px-6 pt-2 pb-6 text-sm text-muted">
          {typeof footer === 'function' ? footer() : footer}
        </div>
      )}
    </Tag>
  );
};

// --------- COMPONENT PROPS
Card.displayName = 'Card';

// --------- EXPORTS
export default Card;
export { Card };
