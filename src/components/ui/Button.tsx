/**
 * @file components/ui/button.tsx
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Reusable Button component with multiple variants and glass morphism styling.
 * Fully accessible with proper ARIA attributes and keyboard navigation.
 * Supports different sizes, variants, and loading states.
 *
 * @since 1.0.0
 */

'use client';

// --------- IMPORTS
import { cn } from '@/lib/utils';
import type { ButtonProps } from '@/types/ui';
import React, { forwardRef } from 'react';

// --------- VARIANT STYLES
const buttonVariants = {
  primary: `
    bg-gradient-to-r from-blue-500/80 to-blue-600/80
    hover:from-blue-400/90 hover:to-blue-500/90
    text-white font-medium
    shadow-lg shadow-blue-500/25 hover:shadow-blue-400/30
    border border-blue-400/30 hover:border-blue-300/40
    backdrop-blur-sm
    active:scale-[0.98] active:shadow-inner
  `,
  secondary: `
    bg-gradient-to-r from-slate-600/60 to-slate-700/60
    hover:from-slate-500/70 hover:to-slate-600/70
    text-slate-100 font-medium
    shadow-lg shadow-slate-600/20 hover:shadow-slate-500/25
    border border-slate-500/30 hover:border-slate-400/40
    backdrop-blur-sm
    active:scale-[0.98] active:shadow-inner
  `,
  outline: `
    bg-transparent hover:bg-glass-light
    text-foreground hover:text-primary-light
    border-2 border-border hover:border-primary/50
    shadow-sm hover:shadow-glow-sm
    backdrop-blur-sm
    active:scale-[0.98] active:bg-glass-strong
  `,
  ghost: `
    bg-transparent hover:bg-glass-light
    text-muted hover:text-foreground
    border border-transparent hover:border-border
    shadow-none hover:shadow-sm
    backdrop-blur-sm
    active:scale-[0.98] active:bg-glass-strong
  `,
  destructive: `
    bg-gradient-to-r from-red-500/80 to-red-600/80
    hover:from-red-400/90 hover:to-red-500/90
    text-white font-medium
    shadow-lg shadow-red-500/25 hover:shadow-red-400/30
    border border-red-400/30 hover:border-red-300/40
    backdrop-blur-sm
    active:scale-[0.98] active:shadow-inner
  `,
  glass: `
    bg-glass-gradient backdrop-blur-md border border-white/10 text-foreground
    hover:bg-glass-strong hover:border-white/20 focus-visible:ring-primary
    shadow-glass-lg hover:shadow-glass-xl
  `,
};

// --------- SIZE STYLES
const buttonSizes = {
  sm: 'px-3 py-1.5 text-sm rounded-lg min-h-[32px]',
  md: 'px-4 py-2 text-base rounded-xl min-h-[40px]',
  lg: 'px-6 py-3 text-lg rounded-xl min-h-[48px]',
  xl: 'px-8 py-4 text-xl rounded-2xl min-h-[56px]',
};

// --------- LOADING SPINNER COMPONENT
const LoadingSpinner: React.FC<{ size?: 'sm' | 'md' | 'lg' | 'xl' }> = ({
  size = 'md',
}) => {
  const spinnerSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
    xl: 'w-6 h-6',
  };

  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2 border-current border-t-transparent',
        spinnerSizes[size]
      )}
      role="status"
      aria-label="Loading"
    >
      <span className="sr-only">Loading...</span>
    </div>
  );
};

// --------- BUTTON COMPONENT
/**
 * Button component with multiple variants and glass morphism styling.
 *
 * @param {ButtonProps} props - Button props
 * @param {'primary'|'secondary'|'outline'|'ghost'|'destructive'|'glass'} [props.variant] - Visual style variant. Default: 'primary'.
 * @param {'sm'|'md'|'lg'|'xl'} [props.size] - Button size. Default: 'md'.
 * @param {boolean} [props.loading] - Show loading spinner and disable button. Default: false.
 * @param {React.ReactNode} [props.loadingIndicator] - Custom loading indicator (replaces default spinner).
 * @param {React.ReactNode} [props.leftIcon] - Icon on the left.
 * @param {React.ReactNode} [props.rightIcon] - Icon on the right.
 * @param {boolean} [props.fullWidth] - Button takes full width. Default: false.
 * @param {React.ReactNode} props.children - Button content.
 * @param {string} [props['aria-label']] - Accessible label for screen readers.
 * @param {string} [props.role] - ARIA role for the button.
 * @param {object} [props.aria-*] - Additional ARIA/accessibility attributes.
 */
const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      loading = false,
      loadingIndicator,
      leftIcon,
      rightIcon,
      fullWidth = false,
      disabled,
      className,
      children,
      type = 'button',
      ...props
    },
    ref
  ) => {
    // Disable button when loading
    const isDisabled = disabled || loading;

    return (
      <button
        ref={ref}
        type={type}
        disabled={isDisabled}
        className={cn(
          // Base styles
          `focus-visible:ring-primary focus-visible:ring-offset-background relative inline-flex items-center justify-center gap-2 overflow-hidden font-medium transition-all duration-300 ease-out select-none focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50`,
          // Variant styles
          buttonVariants[variant],

          // Size styles
          buttonSizes[size],

          // Full width
          fullWidth && 'w-full',

          // Custom className
          className
        )}
        aria-disabled={isDisabled}
        {...props}
      >
        {/* Glass reflection effect overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-white/10 via-transparent to-transparent opacity-0 transition-opacity duration-300 hover:opacity-100" />

        {/* Content container */}
        <div className="relative flex items-center justify-center gap-2">
          {/* Left icon or loading spinner */}
          {loading
            ? loadingIndicator || <LoadingSpinner size={size} />
            : leftIcon && <span className="flex-shrink-0">{leftIcon}</span>}

          {/* Button text */}
          <span className={cn(loading && 'opacity-70')}>{children}</span>

          {/* Right icon (hidden when loading) */}
          {!loading && rightIcon && (
            <span className="flex-shrink-0">{rightIcon}</span>
          )}
        </div>
      </button>
    );
  }
);

// --------- COMPONENT PROPS
Button.displayName = 'Button';

// --------- EXPORTS
export { Button };
