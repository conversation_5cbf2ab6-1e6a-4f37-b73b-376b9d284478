/**
 * @file components/ui/Icon.tsx
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Centralized, scalable Icon component for consistent icon usage in VO-Wiki UI.
 * Easily extendable with new icons. Fully accessible and customizable.
 *
 * @since 1.0.0
 */

'use client';

// --------- IMPORTS
import type { IconProps } from '@/types/ui';
import React from 'react';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';
import {
  FaBeer,
  FaBook,
  FaDiscord,
  FaGithub,
  FaHome,
  FaUser,
} from 'react-icons/fa';
import { HiSparkles } from 'react-icons/hi';
import { MdClose, MdKeyboardArrowUp, MdSearch } from 'react-icons/md';

// --------- ICON MAP
/**
 * Map of available icon names to their respective components.
 * Extend this map as you add more icons.
 */
const ICONS = {
  beer: <PERSON>a<PERSON><PERSON>,
  user: <PERSON>a<PERSON><PERSON>,
  home: FaHome,
  search: MdSearch,
  close: MdClose,
  loading: AiOutlineLoading3Quarters,
  book: FaBook,
  sparkles: HiSparkles,
  github: FaGithub,
  discord: FaDiscord,
  arrowUp: MdKeyboardArrowUp,
} as const;

// --------- COMPONENT
/**
 * Icon component for consistent, accessible icon usage.
 * Add new icons to the ICONS map above.
 */
const Icon: React.FC<IconProps> = ({
  name,
  size = 24,
  color = 'currentColor',
  className = '',
  title,
  'aria-label': ariaLabel,
  ...props
}) => {
  const IconComponent = ICONS[name];
  if (!IconComponent) return null;

  // Accessibility: prefer aria-label, fallback to title, else mark as decorative
  const ariaProps =
    ariaLabel || title
      ? {
          role: 'img',
          'aria-label': ariaLabel || title,
        }
      : {
          'aria-hidden': true,
        };

  return (
    <span
      {...ariaProps}
      className={className}
      style={{ display: 'inline-flex', alignItems: 'center' }}
    >
      <IconComponent size={size} color={color} title={title ?? ''} {...props} />
    </span>
  );
};

// --------- COMPONENT PROPS
Icon.displayName = 'Icon';

// --------- EXPORTS
export default Icon;
export { Icon };
