/**
 * @file components/ui/ImageModal.tsx
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Accessible, glass-themed image modal component for VO-Wiki.
 * Supports keyboard navigation, focus trapping, and responsive design.
 * Displays images with optional captions and close functionality.
 *
 * @since 1.0.0
 */

'use client';

// --------- IMPORTS
import { cn } from '@/lib/utils';
import type { ImageModalProps } from '@/types/ui';
import Image from 'next/image';
import React, { useEffect, useRef } from 'react';

// --------- COMPONENT
const ImageModal: React.FC<ImageModalProps> = ({
  src,
  alt = 'Image',
  open,
  onClose,
  caption,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // Close on ESC
  useEffect(() => {
    if (!open) return;
    const handleKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };
    window.addEventListener('keydown', handleKey);
    return () => window.removeEventListener('keydown', handleKey);
  }, [open, onClose]);

  // Trap focus
  useEffect(() => {
    if (!open || !modalRef.current) return;
    const focusable = modalRef.current.querySelectorAll<HTMLElement>(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    if (focusable.length && focusable[0]) focusable[0].focus();
  }, [open]);

  if (!open) return null;

  return (
    <div
      className="animate-fade-in fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-3xl"
      role="dialog"
      aria-modal="true"
      aria-label="Image modal"
      onClick={onClose}
    >
      <div
        ref={modalRef}
        className={cn(
          'bg-glass-strong border-border/50 shadow-glass-lg relative flex w-full max-w-3xl flex-col items-center rounded-3xl border p-4',
          'animate-zoom-in'
        )}
        onClick={e => e.stopPropagation()}
      >
        <button
          onClick={onClose}
          className="bg-glass-light hover:bg-glass-strong focus-visible:ring-primary absolute top-4 right-4 rounded-full p-2 focus-visible:ring-2"
          aria-label="Close image modal"
        >
          <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
            <path
              d="M6 6l8 8M14 6l-8 8"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
            />
          </svg>
        </button>
        <Image
          src={src}
          alt={alt}
          width={1200}
          height={800}
          className="shadow-glass-md max-h-[70vh] w-auto rounded-2xl object-contain"
          style={{ height: 'auto', width: 'auto', maxHeight: '70vh' }}
        />
        {caption && (
          <div className="text-foreground/80 mt-4 text-center text-sm">
            {caption}
          </div>
        )}
      </div>
    </div>
  );
};

// --------- COMPONENT PROPS
ImageModal.displayName = 'ImageModal';

// --------- EXPORTS
export default ImageModal;
export { ImageModal };
