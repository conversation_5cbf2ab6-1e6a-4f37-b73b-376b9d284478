/**
 * @file components/ui/dropdown.tsx
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Comprehensive dropdown components with glass morphism styling.
 * Includes single-select and multi-select variants with full accessibility support.
 * Features keyboard navigation, search functionality, and smooth animations.
 *
 * @since 1.0.0
 */

'use client';

// --------- IMPORTS
import { useClickOutside } from '@/hooks/useClickOutside';
import { useKeyboardNavigation } from '@/hooks/useKeyboardNavigation';
import { cn } from '@/lib/utils';
import type {
  DropdownOption,
  DropdownProps,
  MultiSelectProps,
  SingleSelectProps,
} from '@/types/ui';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { FaCheck, FaChevronDown, FaSearch, FaTimes } from 'react-icons/fa';

// --------- LOADING SPINNER COMPONENT
const DropdownSpinner: React.FC = () => (
  <div className="flex items-center justify-center py-8">
    <div
      className="w-6 h-6 border-2 rounded-full border-primary animate-spin border-t-transparent"
      role="status"
      aria-label="Loading options"
    >
      <span className="sr-only">Loading...</span>
    </div>
  </div>
);

// --------- SELECTED TAG COMPONENT (for multi-select)
const SelectedTag: React.FC<{
  option: DropdownOption;
  onRemove: () => void;
  disabled?: boolean;
}> = ({ option, onRemove, disabled }) => (
  <span
    className={cn(
      `bg-glass-light border-border text-foreground inline-flex items-center gap-1 rounded-lg border px-2 py-1 text-sm backdrop-blur-sm transition-all duration-200`,
      !disabled && 'hover:bg-glass-strong'
    )}
  >
    {option.icon && <span className="flex-shrink-0">{option.icon}</span>}
    <span className="max-w-[120px] truncate">{option.label}</span>
    {!disabled && (
      <button
        type="button"
        onClick={onRemove}
        className="flex-shrink-0 rounded-sm p-0.5 transition-colors hover:bg-red-500/20"
        aria-label={`Remove ${option.label}`}
      >
        <FaTimes className="w-3 h-3 text-muted hover:text-red-400" />
      </button>
    )}
  </span>
);

// --------- MAIN DROPDOWN COMPONENT
/**
 * Dropdown component with glass morphism styling.
 *
 * @param {DropdownProps} props - Dropdown props (single or multi-select)
 * @param {DropdownOption[]} props.options - List of options
 * @param {string} [props.placeholder] - Placeholder text
 * @param {boolean} [props.disabled] - Disable dropdown. Default: false
 * @param {boolean} [props.searchable] - Enable search. Default: false
 * @param {boolean} [props.clearable] - Allow clearing selection(s). Default: false
 * @param {string} [props.searchPlaceholder] - Search input placeholder
 * @param {boolean|string} [props.error] - Error state
 * @param {string} [props.errorMessage] - Error message
 * @param {string} [props.helpText] - Help text
 * @param {string} [props.label] - Dropdown label
 * @param {boolean} [props.hideLabel] - Visually hide label. Default: false
 * @param {string} [props.className] - Additional class names
 * @param {number|string} [props.maxHeight] - Max dropdown height. Default: '256px'
 * @param {boolean} [props.loading] - Show loading spinner. Default: false
 * @param {string} [props.emptyMessage] - Message when no options
 * @param {boolean} [props.multiple] - Enable multi-select
 * @param {string|number|Array<string|number>} [props.value] - Selected value(s)
 * @param {(value: string|number|undefined) => void} [props.onChange] - Single-select change handler (undefined if clearable)
 * @param {(values: Array<string|number>) => void} [props.onChange] - Multi-select change handler
 * @param {number} [props.maxSelections] - Max selections (multi-select)
 * @param {boolean} [props.showCount] - Show count instead of tags (multi-select). Default: false
 * @param {(option: DropdownOption, selected: boolean, focused: boolean) => React.ReactNode} [props.renderOption] - Custom option renderer
 *
 * Controlled: Provide value/onChange. Uncontrolled: Omit value/onChange. If clearable, onChange may be called with undefined.
 */
const Dropdown = forwardRef<HTMLDivElement, DropdownProps>((props, ref) => {
  const {
    options,
    placeholder = 'Select an option...',
    disabled = false,
    searchable = false,
    searchPlaceholder = 'Search options...',
    error = false,
    errorMessage,
    helpText,
    label,
    hideLabel = false,
    className,
    maxHeight = '256px',
    loading = false,
    emptyMessage = 'No options available',
    multiple = false,
  } = props;

  // Memoize value to avoid conditional assignment in hook dependencies
  const value = useMemo(() => {
    return multiple
      ? (props as MultiSelectProps).value || []
      : (props as SingleSelectProps).value;
  }, [multiple, props]);
  const onChange = multiple
    ? (props as MultiSelectProps).onChange
    : (props as SingleSelectProps).onChange;
  const maxSelections = multiple
    ? (props as MultiSelectProps).maxSelections
    : undefined;
  const showCount = multiple ? (props as MultiSelectProps).showCount : false;

  // Custom option rendering support
  const renderOption = props.renderOption;

  // --------- STATE MANAGEMENT
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // --------- FILTERED OPTIONS
  const filteredOptions = useMemo(() => {
    if (!searchQuery) return options;
    return options.filter(
      option =>
        option.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
        option.description?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [options, searchQuery]);

  // --------- SELECTED OPTIONS
  const selectedOptions = useMemo(() => {
    if (multiple) {
      return options.filter(option =>
        (value as Array<string | number>).includes(option.value)
      );
    }
    return options.filter(option => option.value === value);
  }, [options, value, multiple]);

  // --------- EVENT HANDLERS
  const handleToggle = useCallback(() => {
    if (disabled) return;
    setIsOpen(prev => !prev);
  }, [disabled]);

  const handleClose = useCallback(() => {
    setIsOpen(false);
    setSearchQuery('');
  }, []);

  const handleSelect = useCallback(
    (option: DropdownOption) => {
      if (option.disabled) return;

      if (multiple) {
        const currentValues = value as Array<string | number>;
        const isSelected = currentValues.includes(option.value);

        let newValues: Array<string | number>;
        if (isSelected) {
          newValues = currentValues.filter(v => v !== option.value);
        } else {
          if (maxSelections && currentValues.length >= maxSelections) {
            return; // Max selections reached
          }
          newValues = [...currentValues, option.value];
        }

        (onChange as MultiSelectProps['onChange'])?.(newValues);
      } else {
        (onChange as SingleSelectProps['onChange'])?.(option.value);
        handleClose();
      }
    },
    [multiple, value, onChange, maxSelections, handleClose]
  );

  const handleRemoveTag = useCallback(
    (optionValue: string | number) => {
      if (multiple && onChange) {
        const newValues = (value as Array<string | number>).filter(
          v => v !== optionValue
        );
        (onChange as MultiSelectProps['onChange'])?.(newValues);
      }
    },
    [multiple, value, onChange]
  );

  const handleClearAll = useCallback(() => {
    if (multiple) {
      (onChange as MultiSelectProps['onChange'])?.([]);
    } else {
      (onChange as SingleSelectProps['onChange'])?.(undefined);
    }
  }, [multiple, onChange]);

  // --------- CUSTOM HOOKS
  useClickOutside(dropdownRef, handleClose);
  const { focusedIndex, handleKeyDown } = useKeyboardNavigation(
    filteredOptions,
    isOpen,
    handleSelect,
    handleClose
  );

  // --------- FOCUS MANAGEMENT
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, searchable]);

  // --------- RENDER SELECTED VALUE
  const renderSelectedValue = () => {
    if (multiple) {
      const selectedArray = value as string[];
      if (selectedArray.length === 0) {
        return <span className="text-muted">{placeholder}</span>;
      }

      if (showCount) {
        return (
          <span className="text-foreground">
            {selectedArray.length} item{selectedArray.length !== 1 ? 's' : ''}{' '}
            selected
          </span>
        );
      }

      return (
        <div className="flex flex-wrap max-w-full gap-1">
          {selectedOptions.slice(0, 3).map(option => (
            <SelectedTag
              key={option.value}
              option={option}
              onRemove={() => handleRemoveTag(option.value)}
              disabled={disabled}
            />
          ))}
          {selectedOptions.length > 3 && (
            <span className="px-2 py-1 text-sm text-muted">
              +{selectedOptions.length - 3} more
            </span>
          )}
        </div>
      );
    } else {
      const selectedOption = selectedOptions[0];
      if (!selectedOption) {
        return <span className="text-muted">{placeholder}</span>;
      }

      return (
        <div className="flex items-center gap-2">
          {selectedOption.icon && (
            <span className="flex-shrink-0">{selectedOption.icon}</span>
          )}
          <span className="truncate text-foreground">
            {selectedOption.label}
          </span>
        </div>
      );
    }
  };

  return (
    <div ref={ref} className={cn('relative w-full', className)}>
      {/* Label */}
      {label && (
        <label
          className={cn(
            'text-foreground mb-2 block text-sm font-medium',
            hideLabel && 'sr-only'
          )}
        >
          {label}
        </label>
      )}

      {/* Dropdown Container */}
      <div ref={dropdownRef} className="relative" onKeyDown={handleKeyDown}>
        {/* Trigger Button */}
        <button
          type="button"
          onClick={handleToggle}
          disabled={disabled}
          className={cn(
            `bg-glass-light focus-visible:ring-primary focus-visible:ring-offset-background flex min-h-[48px] w-full items-center justify-between gap-2 rounded-xl border px-4 py-3 text-left backdrop-blur-sm transition-all duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50`,
            error
              ? 'border-red-400/50 hover:border-red-400/70'
              : 'border-border hover:border-primary/50',
            isOpen && 'border-primary/70 shadow-glow-sm',
            disabled && 'hover:border-border'
          )}
          aria-haspopup="listbox"
          aria-expanded={isOpen}
          aria-label={label || 'Select option'}
        >
          <div className="flex-1 min-w-0">{renderSelectedValue()}</div>

          <div className="flex items-center flex-shrink-0 gap-2">
            {/* Clear button for selections */}
            {((multiple && (value as string[]).length > 0) ||
              (!multiple && value)) &&
              !disabled && (
                <span
                  role="button"
                  tabIndex={0}
                  onClick={e => {
                    e.stopPropagation();
                    handleClearAll();
                  }}
                  onKeyDown={e => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleClearAll();
                    }
                  }}
                  className="p-1 transition-colors rounded-md cursor-pointer hover:bg-glass-strong focus-visible:ring-primary focus:outline-none focus-visible:ring-2"
                  aria-label="Clear selection"
                >
                  <FaTimes className="w-3 h-3 text-muted hover:text-foreground" />
                </span>
              )}

            {/* Chevron icon */}
            <FaChevronDown
              className={cn(
                'text-muted h-4 w-4 transition-transform duration-200',
                isOpen && 'rotate-180'
              )}
            />
          </div>
        </button>

        {/* Dropdown Menu */}
        {isOpen && (
          <div
            className={cn(
              `bg-glass-strong border-border/50 animate-in fade-in-0 zoom-in-95 absolute top-full right-0 left-0 z-50 mt-2 origin-top overflow-hidden rounded-xl border shadow-2xl shadow-black/20 backdrop-blur-xl duration-200 ease-out`
            )}
            style={{ maxHeight }}
          >
            {/* Search Input */}
            {searchable && (
              <div className="p-3 border-b border-border/30">
                <div className="relative">
                  <FaSearch className="absolute w-4 h-4 transform -translate-y-1/2 text-muted top-1/2 left-3" />
                  <label
                    id="dropdown-search-label"
                    htmlFor="dropdown-search-input"
                    className="sr-only"
                  >
                    {searchPlaceholder || 'Search options'}
                  </label>
                  <input
                    id="dropdown-search-input"
                    ref={searchInputRef}
                    type="text"
                    placeholder={searchPlaceholder}
                    aria-label={searchPlaceholder || 'Search options'}
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className={cn(
                      `bg-glass-light border-border/50 text-foreground placeholder-muted focus:border-primary/50 focus:ring-primary/25 w-full rounded-lg border py-2 pr-4 pl-10 transition-all duration-200 focus:ring-1 focus:outline-none`
                    )}
                    aria-labelledby="dropdown-search-label"
                  />
                </div>
              </div>
            )}

            {/* Options List */}
            <div
              className="overflow-y-auto"
              style={{ maxHeight: searchable ? 'calc(100% - 64px)' : '100%' }}
              role="listbox"
              aria-multiselectable={multiple}
              aria-label="Dropdown options"
            >
              {loading ? (
                <DropdownSpinner />
              ) : filteredOptions.length === 0 ? (
                <div className="px-4 py-8 text-center text-muted">
                  {emptyMessage}
                </div>
              ) : (
                filteredOptions.map((option, index) => {
                  const isSelected = multiple
                    ? (value as Array<string | number>).includes(option.value)
                    : value === option.value;
                  const isFocused = index === focusedIndex;

                  return (
                    <button
                      key={option.value}
                      type="button"
                      onClick={() => handleSelect(option)}
                      disabled={option.disabled}
                      className={cn(
                        `hover:bg-glass-light focus-visible:bg-glass-light flex w-full items-center gap-3 px-4 py-3 text-left transition-all duration-150 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50`,
                        isSelected && 'bg-primary/10 hover:bg-primary/15',
                        isFocused && 'bg-glass-light',
                        option.disabled && 'hover:bg-transparent'
                      )}
                      role="option"
                      aria-selected={isSelected}
                    >
                      {/* Selection indicator */}
                      <div className="flex items-center justify-center flex-shrink-0 w-5 h-5">
                        {isSelected && (
                          <FaCheck className="w-3 h-3 text-primary" />
                        )}
                      </div>

                      {/* Option icon */}
                      {option.icon && (
                        <span className="flex-shrink-0">{option.icon}</span>
                      )}

                      {/* Option content */}
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate text-foreground">
                          {option.label}
                        </div>
                        {option.description && (
                          <div className="text-muted mt-0.5 truncate text-sm">
                            {option.description}
                          </div>
                        )}
                      </div>

                      {/* Custom option rendering */}
                      {renderOption
                        ? renderOption(option, isSelected, isFocused)
                        : null}
                    </button>
                  );
                })
              )}
            </div>
          </div>
        )}
      </div>

      {/* Help Text */}
      {helpText && !error && (
        <p className="mt-2 text-sm text-muted">{helpText}</p>
      )}

      {/* Error Message */}
      {error && errorMessage && (
        <p className="mt-2 text-sm text-red-400">{errorMessage}</p>
      )}
    </div>
  );
});

// --------- COMPONENT PROPS
Dropdown.displayName = 'Dropdown';

// --------- EXPORTS
export { Dropdown, type DropdownOption, type DropdownProps };
