/**
 * @file components/ui/LoadingSpinner.tsx
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Accessible, animated loading spinner for VO-Wiki UI.
 *
 * @since 1.0.0
 */

'use client';

// --------- IMPORTS
import type { LoadingSpinnerProps } from '@/types/ui';
import { motion } from 'framer-motion';
import * as React from 'react';
import { Icon } from './Icon';

// --------- LOADING SPINNER COMPONENT
/**
 * LoadingSpinner component
 * @see LoadingSpinnerProps in src/types/ui.ts
 */
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 24,
  color = 'currentColor',
  className,
  'aria-label': ariaLabel = 'Loading',
  role = 'status',
  ...rest
}) => {
  // Generate a unique id for aria-describedby
  const spinnerId = React.useId();

  return (
    <motion.span
      className={className}
      aria-label={ariaLabel}
      role={role}
      aria-live="polite"
      aria-busy="true"
      aria-describedby={spinnerId}
      {...rest}
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
      animate={{ rotate: 360 }}
      transition={{ repeat: Infinity, duration: 1, ease: 'linear' }}
    >
      <Icon
        name="loading"
        size={size}
        color={color}
        aria-hidden="true"
        focusable="false"
      />
      <span
        id={spinnerId}
        style={{
          position: 'absolute',
          width: 1,
          height: 1,
          padding: 0,
          margin: -1,
          overflow: 'hidden',
          clip: 'rect(0,0,0,0)',
          whiteSpace: 'nowrap',
          border: 0,
        }}
      >
        {ariaLabel}
      </span>
    </motion.span>
  );
};

// --------- COMPONENT PROPS
LoadingSpinner.displayName = 'LoadingSpinner';

// --------- EXPORTS
export default LoadingSpinner;
export { LoadingSpinner };
