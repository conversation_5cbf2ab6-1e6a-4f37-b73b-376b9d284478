/**
 * @file components/ClientPage.tsx
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * This component serves as the main client-side page for the Velocita Optimized Wiki.
 * It includes a welcome message, a logo, and a button to view the logo in a modal.
 *
 * @since 1.0.0
 */

'use client';

// --------- IMPORTS
import { Button, ImageModal } from '@/components/ui';
import Image from 'next/image';
import { useState } from 'react';

// --------- CLIENT PAGE COMPONENT
export default function ClientPage() {
  const [modalOpen, setModalOpen] = useState(false);
  return (
    <div className="bg-glass-strong grid min-h-screen grid-rows-[20px_1fr_20px] items-center justify-items-center gap-16 p-8 pb-20 font-sans backdrop-blur-xl sm:p-20">
      <main className="row-start-2 flex flex-col items-center gap-8 sm:items-start">
        <h1 className="text-foreground drop-shadow-glow-md text-center text-4xl font-bold sm:text-left">
          Welcome to the Velocita Optimized Wiki
        </h1>
        <p className="text-muted max-w-xl text-center text-lg sm:text-left">
          Explore the documentation and resources for the Velocita Optimized
          Minecraft Modpack.
        </p>
        <div className="bg-glass-light border-border/50 shadow-glass-lg animate-float flex flex-col items-center rounded-2xl border p-6">
          <Image
            src="/images/vo-logo.png"
            alt="Velocita Optimized Logo"
            width={200}
            height={200}
            className="shadow-glass-md rounded-xl object-contain"
            priority
          />
          <Button
            variant="primary"
            size="lg"
            className="mt-6"
            onClick={() => setModalOpen(true)}
            aria-label="View Logo in Modal"
          >
            View Logo
          </Button>
        </div>
        <ImageModal
          src="/images/vo-logo.png"
          alt="Velocita Optimized Logo"
          open={modalOpen}
          onClose={() => setModalOpen(false)}
          caption="Velocita Optimized Modpack Logo"
        />
      </main>
    </div>
  );
}
