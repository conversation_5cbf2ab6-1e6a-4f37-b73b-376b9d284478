/**
 * @file ClientLayout.tsx
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Client-side layout component with improved maintainability,
 * scalability, and proper progress scrollbar integration.
 * Features modular components, configurable theming, and performance optimizations.
 * Includes Liquid Glass Navbar and Footer
 *
 * @since 1.0.0
 */

'use client';

// --------- IMPORTS
import { LiquidFooter } from '@/layout/Footer';
import { LiquidNavbar } from '@/layout/Navbar';
import ProgressScrollbar from '@/ux/animations/ProgressScrollbar';
import React from 'react';

// --- NAVBAR CONFIG ---
const navbarConfig = {
  brand: {
    name: 'VO Wiki',
    icon: 'sparkles',
    href: '/',
  },
  links: [
    { id: 'home', label: 'Home', href: '/', icon: 'home' },
    { id: 'about', label: 'About', href: '/about', icon: 'user' },
    { id: 'contact', label: 'Contact', href: '/contact', icon: 'book' },
    {
      id: 'getting-started',
      label: 'Getting Started',
      href: '/getting-started',
      icon: 'sparkles',
    },
    { id: 'qa', label: 'Q&A', href: '/qa', icon: 'book' },
    { id: 'mods', label: 'Mods', href: '/mods', icon: 'beer' },
    { id: 'guides', label: 'Guides', href: '/guides', icon: 'book' },
  ],
  sticky: true,
  showMobileMenu: true,
  showSearch: true,
  searchPlaceholder: 'Search the wiki...',
};

// --- FOOTER CONFIG ---
const footerConfig = {
  brand: {
    name: 'VO Wiki',
    description:
      'The official wiki for the Velocita Optimized Minecraft Modpack.',
    icon: 'sparkles',
  },
  sections: [
    {
      title: 'Pages',
      icon: 'book',
      links: [
        { id: 'home', label: 'Home', href: '/', icon: 'home' },
        { id: 'about', label: 'About', href: '/about', icon: 'user' },
        { id: 'contact', label: 'Contact', href: '/contact', icon: 'book' },
        {
          id: 'getting-started',
          label: 'Getting Started',
          href: '/getting-started',
          icon: 'sparkles',
        },
        { id: 'qa', label: 'Q&A', href: '/qa', icon: 'book' },
        { id: 'mods', label: 'Mods', href: '/mods', icon: 'beer' },
        { id: 'guides', label: 'Guides', href: '/guides', icon: 'book' },
      ],
    },
  ],
  social: [
    {
      platform: 'GitHub',
      url: 'https://github.com/BleckWolf25/vo-wiki',
      icon: 'github',
      label: 'GitHub',
    },
    {
      platform: 'Discord',
      url: 'https://discord.gg/evhMuYeSYJ',
      icon: 'discord',
      label: 'Discord',
    },
  ],
  copyright: {
    text: 'All rights reserved.',
    year: new Date().getFullYear(),
    holder: 'BleckWolf25',
  },
  legal: [
    { id: 'privacy', label: 'Privacy Policy', href: '/privacy' },
    { id: 'terms', label: 'Terms of Service', href: '/terms' },
  ],
};

// --- PROGRESS SCROLLBAR CONFIG ---
const progressConfig = {
  position: 'fixed-top' as const,
  height: '6px',
  backgroundColor: 'rgba(30,41,59,0.15)',
  progressColor: 'var(--primary)',
  glowEffect: true,
  smoothness: 1,
};

const ClientLayout: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <>
      <ProgressScrollbar config={progressConfig} />
      <LiquidNavbar config={navbarConfig} />
      <main className="flex flex-col min-h-screen pb-10 pt-30">{children}</main>
      <LiquidFooter config={footerConfig} />
    </>
  );
};

// --------- EXPORTS
export default ClientLayout;
export { ClientLayout };
