/**
 * @file components/layout/LiquidNavbar.tsx
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Premium liquid glass navbar with advanced animations, responsive design,
 * and full WCAG 2.1 AA compliance. Features dynamic blur effects, smooth
 * transitions, and interactive hover states with accessibility-first design.
 *
 * @since 1.0.0
 */

'use client';

// --------- IMPORTS
import { Icon } from '@/components/ui/Icon';
import type { LiquidGlassConfig, NavbarConfig } from '@/types/core-types';
import {
  AnimatePresence,
  motion,
  useScroll,
  useTransform,
} from 'framer-motion';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useCallback, useEffect, useRef, useState } from 'react';

// --------- INTERFACES
interface LiquidNavbarProps {
  /** Navbar configuration */
  config: NavbarConfig;
  /** Liquid glass effect settings */
  glassConfig?: LiquidGlassConfig;
  /** Custom className */
  className?: string;
  /** Callback when search is triggered */
  onSearch?: (query: string) => void;
}

// --------- COMPONENT
/**
 * LiquidNavbar - Premium glass navbar with advanced animations
 *
 * Features:
 * - Dynamic scroll-based blur and opacity effects
 * - Responsive mobile menu with smooth animations
 * - WCAG 2.1 AA compliance with keyboard navigation
 * - Search functionality with elegant transitions
 * - Hover effects with liquid glass morphing
 * - Performance optimized with RAF throttling
 */
const LiquidNavbar: React.FC<LiquidNavbarProps> = ({
  config,
  glassConfig = { opacity: 'medium', blur: 'md', glow: true, shimmer: true },
  className = '',
  onSearch,
}) => {
  // --------- STATE & REFS
  const [mobileMenuOpen, setMobileMenuOpen] = useState<boolean>(false);
  const [searchOpen, setSearchOpen] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');

  const pathname = usePathname();
  const navRef = useRef<HTMLElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // --------- SCROLL EFFECTS
  const { scrollY } = useScroll();
  const navOpacity = useTransform(scrollY, [0, 100], [0.9, 0.95]);
  const navBlur = useTransform(scrollY, [0, 100], [8, 16]);

  // --------- HANDLERS
  const toggleMobileMenu = useCallback(() => {
    setMobileMenuOpen(prev => !prev);
  }, []);

  const toggleSearch = useCallback(() => {
    setSearchOpen(prev => {
      const newState = !prev;
      if (newState) {
        // Focus search input when opening
        setTimeout(() => searchInputRef.current?.focus(), 100);
      } else {
        setSearchQuery('');
      }
      return newState;
    });
  }, []);

  const handleSearchSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();
      if (searchQuery.trim()) {
        onSearch?.(searchQuery.trim());
        setSearchOpen(false);
        setSearchQuery('');
      }
    },
    [searchQuery, onSearch]
  );

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (searchOpen) {
          setSearchOpen(false);
          setSearchQuery('');
        } else if (mobileMenuOpen) {
          setMobileMenuOpen(false);
        }
      }
    },
    [searchOpen, mobileMenuOpen]
  );

  // --------- EFFECTS
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (navRef.current && !navRef.current.contains(event.target as Node)) {
        setMobileMenuOpen(false);
        setSearchOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Close mobile menu on route change
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [pathname]);

  // --------- DYNAMIC STYLES
  const getGlassClasses = () => {
    const baseClasses = 'backdrop-blur-md border border-white/10';
    const opacityClasses = {
      light: 'bg-glass-light',
      medium: 'bg-glass',
      strong: 'bg-glass-strong',
    };
    const glowClasses = glassConfig.glow ? 'shadow-glass-md' : '';
    const shimmerClasses = glassConfig.shimmer ? 'glass-shimmer' : '';

    return `${baseClasses} ${opacityClasses[glassConfig.opacity || 'medium']} ${glowClasses} ${shimmerClasses}`;
  };

  const isActiveLink = (href: string) => {
    if (href === '/') return pathname === '/';
    return (pathname ?? '').startsWith(href);
  };

  // --------- RENDER HELPERS
  const renderNavLink = (link: (typeof config.links)[0], isMobile = false) => {
    const isActive = isActiveLink(link.href);
    const baseClasses = isMobile
      ? 'flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 group'
      : 'relative flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 group';

    const activeClasses = isActive
      ? 'bg-primary/20 text-primary shadow-glow-sm'
      : 'hover:bg-white/5 hover:text-primary-light';

    const LinkComponent = link.external ? 'a' : Link;
    const linkProps = link.external
      ? {
          href: link.href,
          target: link.newTab ? '_blank' : undefined,
          rel: link.newTab ? 'noopener noreferrer' : undefined,
        }
      : { href: link.href };

    return (
      <LinkComponent
        key={link.id}
        {...linkProps}
        className={`${baseClasses} ${activeClasses}`}
        aria-label={link.ariaLabel || link.label}
        aria-current={isActive ? 'page' : undefined}
      >
        {link.icon && (
          <Icon
            name={link.icon as any}
            size={isMobile ? 20 : 18}
            className="transition-transform duration-300 group-hover:scale-110"
            aria-hidden="true"
          />
        )}
        <span className={`font-medium ${isMobile ? 'text-base' : 'text-sm'}`}>
          {link.label}
        </span>

        {/* Active indicator */}
        {isActive && !isMobile && (
          <motion.div
            className="bg-primary absolute -bottom-1 left-1/2 h-0.5 w-6 rounded-full"
            layoutId="activeIndicator"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          />
        )}
      </LinkComponent>
    );
  };

  const renderMobileMenu = () => (
    <AnimatePresence>
      {mobileMenuOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            onClick={() => setMobileMenuOpen(false)}
            aria-hidden="true"
          />

          {/* Mobile Menu */}
          <motion.div
            className="fixed z-50 max-w-sm p-6 mx-auto liquid-glass top-20 right-4 left-4 rounded-2xl"
            initial={{ opacity: 0, y: -20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            role="menu"
            aria-label="Mobile navigation menu"
          >
            <nav className="space-y-2" role="menubar">
              {config.links.map(link => (
                <div key={link.id} role="menuitem">
                  {renderNavLink(link, true)}
                </div>
              ))}
            </nav>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );

  const renderSearchBar = () => (
    <AnimatePresence>
      {searchOpen && (
        <motion.div
          className="absolute left-0 right-0 mx-4 mt-2 top-full"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
        >
          <form
            onSubmit={handleSearchSubmit}
            className="p-4 liquid-glass rounded-xl"
          >
            <div className="relative">
              <Icon
                name="search"
                size={20}
                className="absolute text-white -translate-y-1/2 drop-shadow-glow top-1/2 left-3"
                aria-hidden="true"
              />
              <input
                ref={searchInputRef}
                type="search"
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                placeholder={
                  config.searchPlaceholder || 'Search documentation...'
                }
                className="w-full py-3 pl-10 pr-4 transition-all duration-300 bg-transparent border rounded-lg text-foreground placeholder-muted focus:border-primary focus:ring-primary/20 border-white/10 focus:ring-2 focus:outline-none"
                aria-label="Search input"
                autoComplete="off"
              />
              <button
                type="button"
                onClick={toggleSearch}
                className="absolute p-1 text-white transition-colors duration-200 -translate-y-1/2 drop-shadow-glow hover:text-foreground top-1/2 right-3"
                aria-label="Close search"
              >
                <Icon name="close" size={16} />
              </button>
            </div>
          </form>
        </motion.div>
      )}
    </AnimatePresence>
  );

  // --------- RENDER
  return (
    <motion.nav
      ref={navRef}
      className={`fixed top-0 right-0 left-0 z-50 ${getGlassClasses()} ${className}`}
      style={{
        opacity: navOpacity,
        backdropFilter: `blur(${navBlur}px)`,
        WebkitBackdropFilter: `blur(${navBlur}px)`,
      }}
      onKeyDown={handleKeyDown}
      role="navigation"
      aria-label="Main navigation"
    >
      <div className="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Brand/Logo */}
          <Link
            href={config.brand.href}
            className="flex items-center gap-3 group"
            aria-label={`${config.brand.name} homepage`}
          >
            {config.brand.icon && (
              <Icon
                name={config.brand.icon as any}
                size={28}
                className="transition-transform duration-300 text-primary group-hover:scale-110 group-hover:rotate-3"
                aria-hidden="true"
              />
            )}
            <span className="text-xl font-bold transition-colors duration-300 text-foreground group-hover:text-primary-light">
              {config.brand.name}
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="items-center hidden gap-2 md:flex">
            <nav className="flex items-center gap-1" role="menubar">
              {config.links.map(link => (
                <div key={link.id} role="menuitem">
                  {renderNavLink(link)}
                </div>
              ))}
            </nav>

            {/* Search Button */}
            {config.showSearch && (
              <button
                onClick={toggleSearch}
                className="p-2 ml-4 text-white transition-all duration-300 rounded-lg drop-shadow-glow hover:text-foreground group hover:bg-white/5"
                aria-label={searchOpen ? 'Close search' : 'Open search'}
                aria-expanded={searchOpen}
              >
                <Icon
                  name={searchOpen ? 'close' : 'search'}
                  size={20}
                  className="transition-transform duration-300 group-hover:scale-110"
                />
              </button>
            )}
          </div>

          {/* Mobile Menu Button */}
          {config.showMobileMenu && (
            <button
              onClick={toggleMobileMenu}
              className="p-2 text-white transition-all duration-300 rounded-lg drop-shadow-glow hover:text-foreground hover:bg-white/5 md:hidden"
              aria-label={mobileMenuOpen ? 'Close menu' : 'Open menu'}
              aria-expanded={mobileMenuOpen}
              aria-controls="mobile-menu"
            >
              <motion.div
                animate={mobileMenuOpen ? 'open' : 'closed'}
                className="flex flex-col items-center justify-center w-6 h-6"
              >
                <motion.span
                  className="h-0.5 w-6 rounded-full bg-current"
                  variants={{
                    closed: { rotate: 0, y: 0 },
                    open: { rotate: 45, y: 6 },
                  }}
                  transition={{ duration: 0.3 }}
                />
                <motion.span
                  className="mt-1.5 h-0.5 w-6 rounded-full bg-current"
                  variants={{
                    closed: { opacity: 1 },
                    open: { opacity: 0 },
                  }}
                  transition={{ duration: 0.2 }}
                />
                <motion.span
                  className="mt-1.5 h-0.5 w-6 rounded-full bg-current"
                  variants={{
                    closed: { rotate: 0, y: 0 },
                    open: { rotate: -45, y: -6 },
                  }}
                  transition={{ duration: 0.3 }}
                />
              </motion.div>
            </button>
          )}
        </div>
      </div>

      {/* Search Bar */}
      {config.showSearch && renderSearchBar()}

      {/* Mobile Menu */}
      {config.showMobileMenu && renderMobileMenu()}
    </motion.nav>
  );
};

// --------- COMPONENT PROPS
LiquidNavbar.displayName = 'Navbar';

// --------- EXPORTS
export default LiquidNavbar;
export { LiquidNavbar };
