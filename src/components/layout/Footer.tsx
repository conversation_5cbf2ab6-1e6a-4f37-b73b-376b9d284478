/**
 * @file components/layout/LiquidFooter.tsx
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Premium liquid glass footer with elegant sections, social links, and animations.
 * Features responsive design, hover effects, and full WCAG 2.1 AA compliance.
 * Includes brand information, navigation sections, and legal links.
 *
 * @since 1.0.0
 */

'use client';

// --------- IMPORTS
import { Icon } from '@/components/ui/Icon';
import type {
  FooterConfig,
  LiquidGlassConfig,
  SocialLink,
} from '@/types/core-types';
import { motion } from 'framer-motion';
import Link from 'next/link';
import React, { useState } from 'react';

// --------- INTERFACES
interface LiquidFooterProps {
  /** Footer configuration */
  config: FooterConfig;

  /** Liquid glass effect settings */
  glassConfig?: LiquidGlassConfig;

  /** Custom className */
  className?: string;

  /** Show back to top button */
  showBackToTop?: boolean;
}

// --------- COMPONENT
/**
 * LiquidFooter - Premium glass footer with organized sections
 *
 * Features:
 * - Responsive multi-column layout
 * - Animated social media links with hover effects
 * - Organized navigation sections
 * - Brand information and description
 * - Legal links and copyright
 * - Back to top functionality
 * - WCAG 2.1 AA compliance
 * - Liquid glass styling with animations
 */
const LiquidFooter: React.FC<LiquidFooterProps> = ({
  config,
  glassConfig = { opacity: 'medium', blur: 'md', glow: true, shimmer: true },
  className = '',
  showBackToTop = true,
}) => {
  // --------- STATE
  const [hoveredSocial, setHoveredSocial] = useState<string | null>(null);

  // --------- HANDLERS
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  // --------- DYNAMIC STYLES
  const getGlassClasses = () => {
    const baseClasses = 'backdrop-blur-md border-t border-white/10';
    const opacityClasses = {
      light: 'bg-glass-light',
      medium: 'bg-glass',
      strong: 'bg-glass-strong',
    };
    const glowClasses = glassConfig.glow ? 'shadow-glass-md' : '';
    const shimmerClasses = glassConfig.shimmer ? 'glass-shimmer' : '';

    return `${baseClasses} ${opacityClasses[glassConfig.opacity || 'medium']} ${glowClasses} ${shimmerClasses}`;
  };

  // --------- RENDER HELPERS
  const renderSocialLink = (social: SocialLink) => {
    const isHovered = hoveredSocial === social.platform;

    return (
      <motion.a
        key={social.platform}
        href={social.url}
        target="_blank"
        rel="noopener noreferrer"
        className="relative flex items-center justify-center w-10 h-10 transition-all duration-300 border group hover:bg-primary/20 hover:border-primary/30 rounded-xl border-white/10 bg-white/5"
        onMouseEnter={() => setHoveredSocial(social.platform)}
        onMouseLeave={() => setHoveredSocial(null)}
        aria-label={
          social.ariaLabel || `Visit ${config.brand.name} on ${social.label}`
        }
        whileHover={{ scale: 1.05, y: -2 }}
        whileTap={{ scale: 0.95 }}
      >
        <Icon
          name={social.icon as any}
          size={20}
          className={`transition-all duration-300 ${
            isHovered ? 'text-primary' : 'group-hover:text-primary text-white'
          }`}
          aria-hidden="true"
        />
      </motion.a>
    );
  };

  const renderFooterSection = (section: (typeof config.sections)[0]) => (
    <div key={section.title} className="space-y-4">
      <h3 className="flex items-center gap-2 text-lg font-semibold text-foreground">
        {section.icon && (
          <Icon
            name={section.icon as any}
            size={20}
            className="text-primary"
            aria-hidden="true"
          />
        )}
        {section.title}
      </h3>

      <nav
        className="space-y-2"
        role="navigation"
        aria-label={`${section.title} navigation`}
      >
        {section.links.map(link => {
          const LinkComponent = link.external ? 'a' : Link;
          const linkProps = link.external
            ? {
                href: link.href,
                target: link.newTab ? '_blank' : undefined,
                rel: link.newTab ? 'noopener noreferrer' : undefined,
              }
            : { href: link.href };

          return (
            <LinkComponent
              key={link.id}
              {...linkProps}
              className="flex items-center gap-2 text-white transition-colors duration-300 group hover:text-primary-light"
              aria-label={link.ariaLabel || link.label}
            >
              {link.icon && (
                <Icon
                  name={link.icon as any}
                  size={16}
                  className="transition-transform duration-300 group-hover:scale-110"
                  aria-hidden="true"
                />
              )}
              <span className="transition-transform duration-300 group-hover:translate-x-1">
                {link.label}
              </span>
            </LinkComponent>
          );
        })}
      </nav>
    </div>
  );

  const renderLegalLinks = () => {
    if (!config.legal || config.legal.length === 0) return null;

    return (
      <div className="flex flex-wrap justify-center gap-4 md:justify-start">
        {config.legal.map((link, index) => {
          const LinkComponent = link.external ? 'a' : Link;
          const linkProps = link.external
            ? {
                href: link.href,
                target: link.newTab ? '_blank' : undefined,
                rel: link.newTab ? 'noopener noreferrer' : undefined,
              }
            : { href: link.href };

          return (
            <React.Fragment key={link.id}>
              <LinkComponent
                {...linkProps}
                className="text-sm text-white transition-colors duration-300 hover:text-primary-light"
                aria-label={link.ariaLabel || link.label}
              >
                {link.label}
              </LinkComponent>
              {config.legal && index < config.legal.length - 1 && (
                <span className="text-white" aria-hidden="true">
                  •
                </span>
              )}
            </React.Fragment>
          );
        })}
      </div>
    );
  };

  // --------- RENDER
  return (
    <footer
      className={`relative mt-auto ${getGlassClasses()} ${className}`}
      role="contentinfo"
    >
      <div className="px-4 py-10 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div className="flex flex-col gap-10 md:flex-row md:items-start md:justify-between">
          {/* Brand and Description */}
          <div className="mb-8 min-w-[200px] flex-1 md:mb-0">
            <div className="flex items-center gap-3 mb-2">
              {config.brand.icon && (
                <Icon
                  name={config.brand.icon as any}
                  size={28}
                  className="text-primary"
                  aria-hidden="true"
                />
              )}
              <span className="text-xl font-bold text-foreground">
                {config.brand.name}
              </span>
            </div>
            {config.brand.description && (
              <p className="max-w-xs text-sm text-white">
                {config.brand.description}
              </p>
            )}
          </div>

          {/* Navigation Sections */}
          <div className="grid flex-[2] grid-cols-2 gap-8 md:grid-cols-3">
            {config.sections.map(section => renderFooterSection(section))}
          </div>

          {/* Social Links */}
          {config.social && config.social.length > 0 && (
            <div className="flex min-w-[120px] flex-col items-start gap-4">
              <span className="mb-1 text-sm font-semibold text-foreground">
                Follow Us
              </span>
              <div className="flex gap-3">
                {config.social.map(renderSocialLink)}
              </div>
            </div>
          )}
        </div>

        {/* Divider */}
        <div className="my-8 border-t border-white/10" />

        {/* Legal, Copyright, Version, Disclaimer */}
        <div className="flex flex-col gap-4 text-xs text-white md:flex-row md:items-center md:justify-between">
          <div className="flex flex-wrap items-center gap-2">
            <span>
              &copy; {config.copyright.year} {config.copyright.holder}.{' '}
              {config.copyright.text}
            </span>
            <span className="hidden md:inline">|</span>
            <span>Author: BleckWolf25</span>
            <span className="hidden md:inline">|</span>
            <span>Version: 1.0.0</span>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            {renderLegalLinks()}
            <span className="hidden md:inline">|</span>
            <span>
              VO is not affiliated with or endorsed by Mojang or Microsoft.
            </span>
          </div>
        </div>

        {/* Back to Top Button */}
        {showBackToTop && (
          <button
            onClick={scrollToTop}
            className="fixed z-50 p-3 text-white transition-all duration-300 rounded-full shadow-lg bg-primary/80 hover:bg-primary focus:ring-primary/40 right-6 bottom-6 focus:ring-2 focus:outline-none"
            aria-label="Back to top"
          >
            <Icon name="arrowUp" size={20} />
          </button>
        )}
      </div>
    </footer>
  );
};

// --------- COMPONENT PROPS
LiquidFooter.displayName = 'Footer';

// --------- EXPORTS
export default LiquidFooter;
export { LiquidFooter };
