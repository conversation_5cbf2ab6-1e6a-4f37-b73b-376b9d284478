/**
 * @file ux/animations/ParallaxStorytelling.tsx
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Parallax storytelling component with multi-layer scrolling effects.
 * Features liquid glass aesthetics and WCAG 2.1 compliant animations.
 * Optimized for performance with hardware acceleration and reduced motion support.
 *
 * @since 1.0.0
 */

'use client';

// --------- IMPORTS
import type { ParallaxSection, ParallaxStorytellingProps } from '@/types/ux';
import {
  cubicBezier,
  motion,
  useReducedMotion,
  useScroll,
  useTransform,
} from 'framer-motion';
import { useEffect, useRef, useState } from 'react';

// --------- ANIMATION VARIANTS
const sectionVariants = {
  hidden: {
    opacity: 0,
    y: 100,
    scale: 0.95,
    filter: 'blur(10px)',
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    filter: 'blur(0px)',
    transition: {
      duration: 0.8,
      ease: cubicBezier(0.25, 0.46, 0.45, 0.94),
      staggerChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    y: -50,
    scale: 1.05,
    filter: 'blur(5px)',
    transition: {
      duration: 0.6,
      ease: cubicBezier(0.76, 0, 0.24, 1),
    },
  },
};

const contentVariants = {
  hidden: {
    opacity: 0,
    y: 50,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: cubicBezier(0.25, 0.46, 0.45, 0.94),
    },
  },
};

// --------- GLASS EFFECT STYLES
const glassEffectClasses = {
  light: 'liquid-glass-light backdrop-blur-sm',
  medium: 'liquid-glass backdrop-blur-md',
  strong: 'liquid-glass-strong backdrop-blur-lg',
} as const;

// --------- SECTION COMPONENT
interface ParallaxSectionComponentProps {
  section: ParallaxSection;
  index: number;
  scrollY: import('framer-motion').MotionValue<number>;
  enableGlassEffects: boolean;
  reducedMotion: boolean;
  debouncedScrollY?: number; // ADD optional prop for background transform
}

const ParallaxSectionComponent = ({
  section,
  index,
  scrollY,
  enableGlassEffects,
  reducedMotion,
  debouncedScrollY = 0, // default to 0
}: ParallaxSectionComponentProps) => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const [elementTop, setElementTop] = useState(0);
  const [clientHeight, setClientHeight] = useState(0);

  // Calculate parallax transforms
  const initial = elementTop - clientHeight;
  const final = elementTop + (sectionRef.current?.offsetHeight || 0);

  const yRange = useTransform(
    scrollY,
    [initial, final],
    reducedMotion ? [0, 0] : [0, -100 * section.speed]
  );

  const opacityRange = useTransform(
    scrollY,
    [initial - 200, initial + 200, final - 200, final],
    [0, 1, 1, 0]
  );

  const scaleRange = useTransform(
    scrollY,
    [initial, final],
    reducedMotion ? [1, 1] : [0.95, 1.05]
  );

  // Update element position on resize
  useEffect(() => {
    const updateElementTop = () => {
      if (sectionRef.current) {
        setElementTop(sectionRef.current.offsetTop);
        setClientHeight(window.innerHeight);
      }
    };

    updateElementTop();
    window.addEventListener('resize', updateElementTop);
    return () => window.removeEventListener('resize', updateElementTop);
  }, []);

  // Glass effect class
  const glassClass = enableGlassEffects
    ? glassEffectClasses[section.glassEffect || 'medium']
    : '';

  // Height calculation
  const heightClass = {
    auto: 'min-h-[50vh]',
    screen: 'min-h-screen',
    'half-screen': 'min-h-[50vh]',
  }[section.height || 'auto'];

  return (
    <motion.section
      ref={sectionRef}
      variants={sectionVariants}
      initial="hidden"
      whileInView="visible"
      exit="exit"
      viewport={{ once: false, margin: '-100px' }}
      className={`relative ${heightClass} flex items-center justify-center overflow-hidden`}
      style={{
        y: yRange,
        opacity: opacityRange,
        scale: scaleRange,
      }}
      // WCAG 2.1 compliance
      role="region"
      aria-labelledby={`parallax-section-${section.id}`}
      tabIndex={0}
    >
      {/* Background Layer */}
      {section.backgroundImage && (
        <div
          className="absolute inset-0 bg-center bg-no-repeat bg-cover"
          style={{
            backgroundImage: `url(${section.backgroundImage})`,
            transform: reducedMotion
              ? 'none'
              : `translateY(${debouncedScrollY * section.speed * 0.5}px)`,
          }}
          aria-hidden="true"
        />
      )}

      {/* Glass Overlay */}
      <div className={`absolute inset-0 ${glassClass}`} />

      {/* Content Layer */}
      <motion.div
        variants={contentVariants}
        className="relative z-10 w-full max-w-6xl px-6 mx-auto text-center"
      >
        <motion.h2
          id={`parallax-section-${section.id}`}
          className="mb-8 text-4xl font-bold text-foreground drop-shadow-lg md:text-6xl"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: false }}
        >
          {section.title}
        </motion.h2>

        <motion.div
          className="text-lg text-foreground/90 drop-shadow-md md:text-xl"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: false }}
        >
          {section.content}
        </motion.div>

        {/* Floating Elements */}
        <motion.div
          className="absolute inset-0 pointer-events-none -z-10"
          animate={
            reducedMotion
              ? {}
              : {
                  y: [0, -10, 0],
                  rotate: [0, 1, 0],
                }
          }
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          aria-hidden="true"
        >
          <div className="absolute w-20 h-20 rounded-full bg-primary/10 animate-pulse-glow top-1/4 left-1/4 blur-xl" />
          <div className="absolute w-32 h-32 rounded-full bg-accent/10 animate-float right-1/4 bottom-1/4 blur-2xl" />
        </motion.div>
      </motion.div>

      {/* Section Navigation Indicator */}
      <motion.div
        className="absolute transform -translate-x-1/2 bottom-8 left-1/2"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ delay: 1 }}
      >
        <div className="flex space-x-2">
          {Array.from({ length: 3 }).map((_, dotIndex) => (
            <div
              key={dotIndex}
              className={`h-2 w-2 rounded-full transition-all duration-300 ${
                dotIndex === index % 3
                  ? 'bg-primary shadow-glow-sm'
                  : 'bg-muted/50'
              }`}
              aria-hidden="true"
            />
          ))}
        </div>
      </motion.div>
    </motion.section>
  );
};

// --------- MAIN COMPONENT
export const ParallaxStorytelling = ({
  sections,
  containerClassName = '',
  enableGlassEffects = true,
  reducedMotion: forcedReducedMotion,
  className = '',
  children,
  ...motionProps
}: ParallaxStorytellingProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const prefersReducedMotion = useReducedMotion();
  const reducedMotion = forcedReducedMotion ?? prefersReducedMotion ?? false;

  // Scroll tracking with performance optimization
  const { scrollY } = useScroll({
    target: containerRef,
    offset: ['start end', 'end start'],
  });

  // Debounced scroll handler for performance (used for background transform only)
  const [debouncedScrollY, setDebouncedScrollY] = useState(0);
  useEffect(() => {
    const unsubscribe = scrollY.on('change', latest => {
      setDebouncedScrollY(latest);
    });
    return unsubscribe;
  }, [scrollY]);

  // Announce changes to screen readers
  const [currentSection, setCurrentSection] = useState('');

  useEffect(() => {
    const announceSection = (sectionTitle: string) => {
      if (sectionTitle !== currentSection) {
        setCurrentSection(sectionTitle);
        // Could integrate with a live region for announcements
      }
    };

    // Intersection observer for accessibility announcements
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const sectionId = entry.target.getAttribute('aria-labelledby');
            const section = sections.find(
              s => `parallax-section-${s.id}` === sectionId
            );
            if (section) {
              announceSection(section.title);
            }
          }
        });
      },
      { threshold: 0.5 }
    );

    // Observe all sections after initial render
    const timer = setTimeout(() => {
      const sectionElements =
        containerRef.current?.querySelectorAll('[role="region"]');
      sectionElements?.forEach(el => observer.observe(el));
    }, 100);

    return () => {
      clearTimeout(timer);
      observer.disconnect();
    };
  }, [sections, currentSection]);

  return (
    <motion.div
      ref={containerRef}
      className={`relative w-full overflow-hidden ${containerClassName} ${className}`}
      {...motionProps}
      // WCAG 2.1 compliance
      role="main"
      aria-label="Parallax storytelling sections"
    >
      {/* Skip link for accessibility */}
      <a
        href="#parallax-content-end"
        className="z-50 px-4 py-2 rounded-lg sr-only bg-primary text-background focus:not-sr-only focus:absolute focus:top-4 focus:left-4"
      >
        Skip parallax content
      </a>

      {/* Render sections */}
      {sections.map((section, index) => (
        <ParallaxSectionComponent
          key={section.id}
          section={section}
          index={index}
          scrollY={scrollY}
          enableGlassEffects={enableGlassEffects}
          reducedMotion={reducedMotion}
          debouncedScrollY={debouncedScrollY}
        />
      ))}

      {/* Additional content */}
      {children}

      {/* End marker for skip link */}
      <div id="parallax-content-end" tabIndex={-1} aria-hidden="true" />

      {/* Live region for announcements */}
      <div
        className="sr-only"
        role="status"
        aria-live="polite"
        aria-atomic="true"
      >
        {currentSection && `Now viewing: ${currentSection}`}
      </div>
    </motion.div>
  );
};

export default ParallaxStorytelling;
