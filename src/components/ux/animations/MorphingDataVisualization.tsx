/**
 * @file ux/animations/MorphingDataVisualization.tsx
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Complete morphing data visualization component with smooth transitions between different data sets.
 * Features liquid glass aesthetics, WCAG 2.1 compliance, and performance optimizations.
 * Supports charts, counters, progress bars, and statistical displays perfect for modpack data.
 *
 * @since 1.0.0
 */

'use client';

// --------- IMPORTS
import type { MorphingDataVisualizationProps } from '@/types/ux';
import {
  AnimatePresence,
  cubicBezier,
  motion,
  useMotionValue,
  useReducedMotion,
  useTransform,
} from 'framer-motion';
import { useCallback, useEffect, useMemo, useState } from 'react';

// --------- ANIMATION VARIANTS
const containerVariants = {
  hidden: {
    opacity: 0,
    scale: 0.9,
    filter: 'blur(10px)',
  },
  visible: {
    opacity: 1,
    scale: 1,
    filter: 'blur(0px)',
    transition: {
      duration: 0.6,
      ease: cubicBezier(0.25, 0.46, 0.45, 0.94),
      staggerChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    scale: 1.1,
    filter: 'blur(5px)',
    transition: {
      duration: 0.4,
      ease: cubicBezier(0.76, 0, 0.24, 1),
    },
  },
};

const itemVariants = {
  hidden: {
    opacity: 0,
    y: 20,
    scale: 0.8,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: cubicBezier(0.25, 0.46, 0.45, 0.94),
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    scale: 0.8,
    transition: {
      duration: 0.3,
      ease: cubicBezier(0.76, 0, 0.24, 1),
    },
  },
};

// --------- COUNTER COMPONENT
interface AnimatedCounterProps {
  value: number;
  duration: number;
  label: string;
  suffix?: string;
  prefix?: string;
  theme: 'dark' | 'light' | 'glass';
}

const AnimatedCounter = ({
  value,
  duration,
  label,
  suffix = '',
  prefix = '',
  theme,
}: AnimatedCounterProps) => {
  const [displayValue, setDisplayValue] = useState(0);
  const prefersReducedMotion = useReducedMotion();
  const animationDuration = prefersReducedMotion ? 0.1 : duration;

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / (animationDuration * 1000), 1);

      // Easing function for smooth animation
      const easedProgress = 1 - Math.pow(1 - progress, 3);
      const currentValue = Math.floor(easedProgress * value);

      setDisplayValue(currentValue);

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [value, animationDuration]);

  const themeClasses = {
    dark: 'text-foreground',
    light: 'text-gray-900',
    glass: 'text-foreground drop-shadow-lg',
  };

  return (
    <motion.div
      className="text-center"
      variants={itemVariants}
      whileHover={{ scale: 1.05 }}
      transition={{ type: 'spring', stiffness: 300, damping: 20 }}
    >
      <motion.div
        className={`text-4xl font-bold md:text-6xl ${themeClasses[theme]} mb-2`}
        initial={{ scale: 0.8 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        {prefix}
        {displayValue.toLocaleString()}
        {suffix}
      </motion.div>
      <div className={`text-sm md:text-base ${themeClasses[theme]} opacity-80`}>
        {label}
      </div>
    </motion.div>
  );
};

// --------- PROGRESS BAR COMPONENT
interface AnimatedProgressBarProps {
  percentage: number;
  duration: number;
  label: string;
  theme: 'dark' | 'light' | 'glass';
  showValue?: boolean;
}

const AnimatedProgressBar = ({
  percentage,
  duration,
  label,
  theme,
  showValue = true,
}: AnimatedProgressBarProps) => {
  const progress = useMotionValue(0);
  const width = useTransform(progress, [0, 100], ['0%', '100%']);
  const prefersReducedMotion = useReducedMotion();

  useEffect(() => {
    const animationDuration = prefersReducedMotion ? 0.1 : duration;
    let start: number | null = null;
    let frame: number;
    const from = progress.get();
    const to = percentage;
    const animate = (timestamp: number) => {
      if (start === null) start = timestamp;
      const elapsed = (timestamp - start) / (animationDuration * 1000);
      const eased = Math.min(1, 1 - Math.pow(1 - elapsed, 3));
      progress.set(from + (to - from) * eased);
      if (elapsed < 1) {
        frame = requestAnimationFrame(animate);
      }
    };
    frame = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(frame);
  }, [percentage, duration, progress, prefersReducedMotion]);

  const themeClasses = {
    dark: {
      bg: 'bg-accent/20',
      fill: 'bg-gradient-to-r from-primary to-primary-dark',
      text: 'text-foreground',
    },
    light: {
      bg: 'bg-gray-200',
      fill: 'bg-gradient-to-r from-blue-500 to-blue-600',
      text: 'text-gray-900',
    },
    glass: {
      bg: 'bg-glass backdrop-blur-sm',
      fill: 'bg-gradient-to-r from-primary/80 to-primary-dark/80 backdrop-blur-sm',
      text: 'text-foreground drop-shadow-lg',
    },
  };

  return (
    <motion.div
      className="w-full"
      variants={itemVariants}
      whileHover={{ scale: 1.02 }}
    >
      <div
        className={`mb-2 flex items-center justify-between ${themeClasses[theme].text}`}
      >
        <span className="text-sm font-medium">{label}</span>
        {showValue && (
          <motion.span
            className="text-sm font-bold"
            key={percentage}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            {percentage}%
          </motion.span>
        )}
      </div>

      <div
        className={`h-3 overflow-hidden rounded-full ${themeClasses[theme].bg}`}
      >
        <motion.div
          className={`h-full rounded-full ${themeClasses[theme].fill} shadow-glow-sm`}
          style={{ width }}
          initial={{ x: '-100%' }}
          animate={{ x: '0%' }}
          transition={{
            duration: prefersReducedMotion ? 0.1 : duration,
            ease: cubicBezier(0.25, 0.46, 0.45, 0.94),
            delay: 0.2,
          }}
        />
      </div>
    </motion.div>
  );
};

// --------- CHART COMPONENTS
interface AnimatedChartProps {
  data: Array<{ label: string; value: number; color?: string }>;
  duration: number;
  theme: 'dark' | 'light' | 'glass';
  type: 'bar' | 'line' | 'pie';
  maxValue?: number;
}

const AnimatedBarChart = ({
  data,
  duration,
  theme,
  maxValue,
}: Omit<AnimatedChartProps, 'type'>) => {
  const prefersReducedMotion = useReducedMotion();
  const animationDuration = prefersReducedMotion ? 0.1 : duration;
  const max =
    typeof maxValue === 'number'
      ? maxValue
      : Math.max(...data.map(d => d.value));

  const themeClasses = {
    dark: {
      bg: 'bg-accent/20',
      bar: 'bg-gradient-to-t from-primary to-primary-light',
      text: 'text-foreground',
    },
    light: {
      bg: 'bg-gray-200',
      bar: 'bg-gradient-to-t from-blue-500 to-blue-400',
      text: 'text-gray-900',
    },
    glass: {
      bg: 'bg-glass backdrop-blur-sm',
      bar: 'bg-gradient-to-t from-primary/80 to-primary-light/80 backdrop-blur-sm',
      text: 'text-foreground drop-shadow-lg',
    },
  };

  return (
    <div className="space-y-4">
      {data.map((item, index) => {
        const height = (item.value / max) * 100;
        return (
          <motion.div
            key={item.label}
            className="flex items-center space-x-4"
            variants={itemVariants}
            custom={index}
          >
            <div className={`w-20 text-sm ${themeClasses[theme].text}`}>
              {item.label}
            </div>
            <div className="flex-1">
              <div
                className={`h-8 overflow-hidden rounded-lg ${themeClasses[theme].bg}`}
              >
                <motion.div
                  className={`h-full rounded-lg ${themeClasses[theme].bar} shadow-glow-sm`}
                  initial={{ width: '0%' }}
                  animate={{ width: `${height}%` }}
                  transition={{
                    duration: animationDuration,
                    delay: index * 0.1,
                    ease: [0.25, 0.46, 0.45, 0.94],
                  }}
                />
              </div>
            </div>
            <div
              className={`w-16 text-right text-sm ${themeClasses[theme].text}`}
            >
              {item.value.toLocaleString()}
            </div>
          </motion.div>
        );
      })}
    </div>
  );
};

const AnimatedPieChart = ({
  data,
  duration,
  theme,
}: Omit<AnimatedChartProps, 'type' | 'maxValue'>) => {
  const prefersReducedMotion = useReducedMotion();
  const animationDuration = prefersReducedMotion ? 0.1 : duration;
  const total = data.reduce((sum, item) => sum + item.value, 0);

  const themeClasses = {
    dark: 'text-foreground',
    light: 'text-gray-900',
    glass: 'text-foreground drop-shadow-lg',
  };

  let cumulativePercentage = 0;

  return (
    <div className="flex flex-col items-center space-y-4 md:flex-row md:space-y-0 md:space-x-8">
      {/* Pie Chart SVG */}
      <motion.div
        className="relative"
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ duration: animationDuration, ease: 'easeInOut' }}
      >
        <svg width="200" height="200" className="transform -rotate-90">
          <circle
            cx="100"
            cy="100"
            r="80"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            className="opacity-20"
          />
          {data.map((item, index) => {
            const percentage = (item.value / total) * 100;
            const strokeDasharray = `${percentage * 5.02} 500`;
            const strokeDashoffset = -cumulativePercentage * 5.02;
            cumulativePercentage += percentage;

            return (
              <motion.circle
                key={item.label}
                cx="100"
                cy="100"
                r="80"
                fill="none"
                stroke={item.color || `hsl(${index * 60}, 70%, 50%)`}
                strokeWidth="12"
                strokeDasharray={strokeDasharray}
                strokeDashoffset={strokeDashoffset}
                className="opacity-80"
                initial={{ strokeDasharray: '0 500' }}
                animate={{ strokeDasharray }}
                transition={{
                  duration: animationDuration,
                  delay: index * 0.2,
                  ease: [0.25, 0.46, 0.45, 0.94],
                }}
              />
            );
          })}
        </svg>
      </motion.div>

      {/* Legend */}
      <div className="space-y-2">
        {data.map((item, index) => (
          <motion.div
            key={item.label}
            className="flex items-center space-x-3"
            variants={itemVariants}
            custom={index}
          >
            <div
              className="w-4 h-4 rounded-full"
              style={{
                backgroundColor: item.color || `hsl(${index * 60}, 70%, 50%)`,
              }}
            />
            <span className={`text-sm ${themeClasses[theme]}`}>
              {item.label}: {((item.value / total) * 100).toFixed(1)}%
            </span>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

// --------- MAIN COMPONENT
const MorphingDataVisualization = ({
  config,
  onMorphComplete,
  interactive = true,
  theme = 'glass',
  className = '',
  children,
  ...motionProps
}: MorphingDataVisualizationProps) => {
  const [isVisible, setIsVisible] = useState(false);

  // Handle morph completion callback
  const handleMorphComplete = useCallback(() => {
    onMorphComplete?.();
  }, [onMorphComplete]);

  // Memoize chart component for performance
  const ChartComponent = useMemo(() => {
    switch (config.type) {
      case 'counter':
        return (
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {config.data.map((item, index) => (
              <AnimatedCounter
                key={`${item.label}-${index}`}
                value={item.value}
                duration={config.animationDuration}
                label={item.label}
                suffix={item.suffix}
                prefix={item.prefix}
                theme={theme}
              />
            ))}
          </div>
        );

      case 'progress':
        return (
          <div className="space-y-6">
            {config.data.map((item, index) => (
              <AnimatedProgressBar
                key={`${item.label}-${index}`}
                percentage={item.value}
                duration={config.animationDuration}
                label={item.label}
                theme={theme}
                showValue={true}
              />
            ))}
          </div>
        );

      case 'chart':
        const chartType = (config as any).chartType || 'bar';
        if (chartType === 'pie') {
          return (
            <AnimatedPieChart
              data={config.data}
              duration={config.animationDuration}
              theme={theme}
            />
          );
        }
        return (
          <AnimatedBarChart
            data={config.data}
            duration={config.animationDuration}
            theme={theme}
            maxValue={(config as any).maxValue}
          />
        );

      case 'stats':
        return (
          <div className="grid grid-cols-2 gap-6 md:grid-cols-4">
            {config.data.map((item, index) => (
              <motion.div
                key={`${item.label}-${index}`}
                className={`rounded-2xl p-6 ${
                  theme === 'glass'
                    ? 'liquid-glass'
                    : theme === 'dark'
                      ? 'bg-accent/20'
                      : 'bg-gray-100'
                }`}
                variants={itemVariants}
                whileHover={interactive ? { scale: 1.05 } : {}}
                whileTap={interactive ? { scale: 0.95 } : {}}
              >
                <div className="mb-2 text-2xl font-bold text-primary">
                  {item.value.toLocaleString()}
                </div>
                <div className="text-sm text-foreground/80">{item.label}</div>
              </motion.div>
            ))}
          </div>
        );

      default:
        return <div>Unsupported chart type</div>;
    }
  }, [config, theme, interactive]);

  // Trigger animation on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  // Handle morph trigger events
  useEffect(() => {
    if (!config.morphTrigger || config.morphTrigger === 'auto') return;

    const handleTrigger = () => {
      setIsVisible(false);
      setTimeout(() => {
        setIsVisible(true);
        handleMorphComplete();
      }, 300);
    };

    if (config.morphTrigger === 'scroll') {
      // Scroll trigger would be handled by parent component
      return;
    }

    if (config.morphTrigger === 'hover' || config.morphTrigger === 'click') {
      const element = document.getElementById(config.id);
      if (element) {
        const eventType =
          config.morphTrigger === 'hover' ? 'mouseenter' : 'click';
        element.addEventListener(eventType, handleTrigger);
        return () => element.removeEventListener(eventType, handleTrigger);
      }
    }
    // Always return undefined if no cleanup
    return undefined;
  }, [config.morphTrigger, config.id, handleMorphComplete]);

  return (
    <motion.div
      id={config.id}
      className={`w-full ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate={isVisible ? 'visible' : 'hidden'}
      exit="exit"
      onAnimationComplete={handleMorphComplete}
      {...motionProps}
      // WCAG 2.1 compliance
      role="region"
      aria-label={`Data visualization: ${config.type}`}
      tabIndex={interactive ? 0 : -1}
    >
      <AnimatePresence mode="wait">
        {isVisible && (
          <motion.div
            key={`${config.id}-${config.type}`}
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            {ChartComponent}
          </motion.div>
        )}
      </AnimatePresence>

      {children}

      {/* Live region for accessibility */}
      <div
        className="sr-only"
        role="status"
        aria-live="polite"
        aria-atomic="true"
      >
        {isVisible && `Data visualization updated: ${config.type}`}
      </div>
    </motion.div>
  );
};

// --------- EXPORTS
export default MorphingDataVisualization;
export { MorphingDataVisualization };
