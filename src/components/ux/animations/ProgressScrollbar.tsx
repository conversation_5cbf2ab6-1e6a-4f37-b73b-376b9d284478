/**
 * @file ux/animations/ProgressScrollbar.tsx
 *
 * @version 2.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Animated progress scroll bar with premium liquid/glass effects.
 * Features near-instantaneous tracking with subtle easing, 100% completion celebration,
 * improved liquid glass styling, and high-performance optimizations.
 * WCAG 2.1 compliant with advanced a11y features.
 *
 * @since 2.0.0
 */

'use client';

// --------- IMPORTS
import type { ProgressScrollBarProps } from '@/types/ux';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import React, { useCallback, useEffect, useRef, useState } from 'react';

// --------- INTERFACES
interface CelebrationState {
  isActive: boolean;
  particles: Array<{
    id: number;
    x: number;
    y: number;
    vx: number;
    vy: number;
    life: number;
    maxLife: number;
  }>;
}

// --------- COMPONENT
/**
 * ProgressScrollbar
 *
 * Performance optimizations:
 * - RAF-throttled scroll handling
 * - CSS transforms for GPU acceleration
 * - Optimized motion values with spring physics
 * - Memoized callbacks to prevent unnecessary re-renders
 *
 * Visual:
 * - Improved liquid glass effect with dynamic intensity
 * - Full-width coverage with elegant rounded corners
 * - 100% completion celebration with particle effects
 * - Smooth gradient transitions and glow effects
 *
 * @param {ProgressScrollBarProps} props
 */
const ProgressScrollbar: React.FC<ProgressScrollBarProps> = ({
  config,
  showPercentage = false,
  onProgressChange,
}: ProgressScrollBarProps) => {
  // --------- STATE & REFS
  const [progress, setProgress] = useState<number>(0);
  const [isComplete, setIsComplete] = useState<boolean>(false);
  const [celebration, setCelebration] = useState<CelebrationState>({
    isActive: false,
    particles: [],
  });

  const barRef = useRef<HTMLDivElement>(null);
  const rafRef = useRef<number | undefined>(undefined);
  const lastScrollY = useRef<number>(0);
  const completionTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  // --------- MOTION VALUES WITH SPRING PHYSICS
  const progressMotion = useMotionValue(0);
  const springProgress = useSpring(progressMotion, {
    stiffness: 300,
    damping: 30,
    mass: 0.8,
  });

  const widthMotion = useTransform(springProgress, (v: number) => `${v}%`);
  const glowIntensity = useTransform(springProgress, [0, 100], [0.3, 1]);
  const blurIntensity = useTransform(springProgress, [0, 100], [8, 16]);

  // --------- CELEBRATION EFFECT
  const triggerCelebration = useCallback(() => {
    if (celebration.isActive) return;

    const particles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: window.innerHeight * 0.1, // Start from progress bar area
      vx: (Math.random() - 0.5) * 8,
      vy: Math.random() * -4 - 2,
      life: 0,
      maxLife: 60 + Math.random() * 40,
    }));

    setCelebration({ isActive: true, particles });

    // Animate particles
    const animateParticles = () => {
      setCelebration(prev => {
        const updatedParticles = prev.particles
          .map(particle => ({
            ...particle,
            x: particle.x + particle.vx,
            y: particle.y + particle.vy,
            vy: particle.vy + 0.1, // Gravity
            life: particle.life + 1,
          }))
          .filter(particle => particle.life < particle.maxLife);

        if (updatedParticles.length > 0) {
          requestAnimationFrame(animateParticles);
        } else {
          return { isActive: false, particles: [] };
        }

        return { ...prev, particles: updatedParticles };
      });
    };

    requestAnimationFrame(animateParticles);
  }, [celebration.isActive]);

  // --------- OPTIMIZED SCROLL HANDLER
  const handleScroll = useCallback(() => {
    const scrollTop = window.scrollY;
    const docHeight =
      document.documentElement.scrollHeight - window.innerHeight;

    if (docHeight <= 0) return;

    const rawProgress = Math.min(
      Math.max((scrollTop / docHeight) * 100, 0),
      100
    );
    const roundedProgress = Math.round(rawProgress * 100) / 100; // Round to 2 decimals

    // Only update if progress changed significantly (debounce micro-updates)
    if (Math.abs(roundedProgress - lastScrollY.current) > 0.1) {
      lastScrollY.current = roundedProgress;
      setProgress(roundedProgress);
      progressMotion.set(roundedProgress);
      onProgressChange?.(roundedProgress);

      // Handle completion state
      if (roundedProgress >= 100 && !isComplete) {
        setIsComplete(true);
        clearTimeout(completionTimeoutRef.current);
        completionTimeoutRef.current = setTimeout(() => {
          triggerCelebration();
        }, 200);
      } else if (roundedProgress < 100 && isComplete) {
        setIsComplete(false);
        clearTimeout(completionTimeoutRef.current);
      }
    }
  }, [isComplete, onProgressChange, progressMotion, triggerCelebration]);

  // --------- RAF-THROTTLED SCROLL LISTENER
  useEffect(() => {
    const throttledScroll = () => {
      if (rafRef.current) return;

      rafRef.current = requestAnimationFrame(() => {
        handleScroll();
        rafRef.current = undefined;
      });
    };

    window.addEventListener('scroll', throttledScroll, { passive: true });

    // Initial calculation
    handleScroll();

    return () => {
      window.removeEventListener('scroll', throttledScroll);
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
      clearTimeout(completionTimeoutRef.current);
    };
  }, [handleScroll]);

  // --------- POSITION LOGIC
  const positionStyles = {
    position: 'fixed' as const,
    top: config.position === 'bottom' ? undefined : '4.05rem', // Adjusted to 4.05rem for correct Y
    bottom: config.position === 'bottom' ? '0' : undefined,
    left: '0',
    right: '0',
    zIndex: 40, // Below navbar (z-50)
  };

  // --------- DYNAMIC STYLING
  const dynamicBarStyles = {
    height: config.height || '6px',
    background: `linear-gradient(135deg,
      ${config.backgroundColor || 'rgba(30, 41, 59, 0.1)'} 0%,
      rgba(30, 41, 59, 0.05) 100%)`,
    backdropFilter: `blur(${blurIntensity.get()}px) saturate(1.2)`,
    WebkitBackdropFilter: `blur(${blurIntensity.get()}px) saturate(1.2)`,
    boxShadow: config.glowEffect
      ? `0 0 20px 2px ${config.progressColor || 'var(--primary)'}${Math.round(
          glowIntensity.get() * 0.4 * 255
        )
          .toString(16)
          .padStart(2, '0')},
         0 2px 12px 0 ${config.progressColor || 'var(--primary)'}${Math.round(
           glowIntensity.get() * 0.2 * 255
         )
           .toString(16)
           .padStart(2, '0')}`
      : '0 2px 8px rgba(0, 0, 0, 0.1)',
    borderRadius: '0 0 1rem 1rem',
    border: '1px solid rgba(255, 255, 255, 0.1)',
    overflow: 'hidden',
  };

  const progressBarStyles = {
    background: isComplete
      ? `linear-gradient(90deg,
          ${config.progressColor || 'var(--primary)'},
          var(--primary-light),
          ${config.progressColor || 'var(--primary)'})`
      : `linear-gradient(90deg,
          ${config.progressColor || 'var(--primary)'} 0%,
          var(--primary-light) 50%,
          ${config.progressColor || 'var(--primary)'} 100%)`,
    backgroundSize: isComplete ? '200% 100%' : '100% 100%',
    boxShadow: config.glowEffect
      ? `inset 0 1px 0 rgba(255, 255, 255, 0.3),
         0 0 20px ${config.progressColor || 'var(--primary)'}${Math.round(
           glowIntensity.get() * 0.6 * 255
         )
           .toString(16)
           .padStart(2, '0')},
         0 2px 8px ${config.progressColor || 'var(--primary)'}${Math.round(
           glowIntensity.get() * 0.3 * 255
         )
           .toString(16)
           .padStart(2, '0')}`
      : undefined,
    borderRadius: 'inherit',
    position: 'relative' as const,
    overflow: 'hidden',
  };

  // --------- RENDER
  return (
    <>
      {/* Main Progress Bar */}
      <div
        ref={barRef}
        className="pointer-events-none w-full select-none"
        style={positionStyles}
        role="progressbar"
        aria-valuenow={Math.round(progress)}
        aria-valuemin={0}
        aria-valuemax={100}
        aria-label="Page scroll progress"
        aria-live="polite"
        tabIndex={-1}
      >
        <div style={dynamicBarStyles}>
          {/* Shimmer Effect Overlay */}
          <div className="absolute inset-0 opacity-30">
            <div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              style={{
                backgroundSize: '200% 100%',
                animation: 'shimmer 6s ease-in-out infinite', // Slower shimmer
                backgroundPosition: '-200% 0',
              }}
            />
          </div>

          {/* Progress Fill */}
          <motion.div
            style={{
              width: widthMotion,
              height: '100%',
              ...progressBarStyles,
            }}
            className={isComplete ? 'animate-pulse' : ''}
            animate={
              isComplete
                ? {
                    backgroundPosition: ['0% 0%', '200% 0%'],
                  }
                : {}
            }
            transition={{
              duration: 2,
              repeat: isComplete ? Infinity : 0,
              ease: 'linear',
            }}
          >
            {/* Inner glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-50" />

            {/* Completion pulse effect */}
            {isComplete && (
              <motion.div
                className="absolute inset-0 bg-white/20"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: [0, 0.5, 0], scale: [0.8, 1.2, 1] }}
                transition={{ duration: 0.6, ease: 'easeOut' }}
              />
            )}
          </motion.div>

          {/* Percentage Display */}
          {showPercentage && (
            <motion.span
              className="absolute top-1/2 right-4 -translate-y-1/2 rounded-md bg-black/40 px-2 py-0.5 font-mono text-xs font-bold text-white drop-shadow-lg"
              initial={{ opacity: 0 }}
              animate={{ opacity: progress > 5 ? 1 : 0 }}
              transition={{ duration: 0.3 }}
              aria-live="polite"
            >
              {Math.round(progress)}%
            </motion.span>
          )}
        </div>
      </div>

      {/* Celebration Particles */}
      {celebration.isActive && (
        <div className="pointer-events-none fixed inset-0 z-50">
          {celebration.particles.map(particle => (
            <motion.div
              key={particle.id}
              className="bg-primary absolute h-2 w-2 rounded-full"
              style={{
                left: particle.x,
                top: particle.y,
                opacity: 1 - particle.life / particle.maxLife,
                boxShadow: `0 0 8px ${config.progressColor || 'var(--primary)'}`,
              }}
              animate={{
                scale: [1, 0.5],
                opacity: [1, 0],
              }}
              transition={{
                duration: particle.maxLife / 60,
                ease: 'easeOut',
              }}
            />
          ))}
        </div>
      )}

      {/* Custom Styles */}
      <style jsx>{`
        @keyframes shimmer {
          0%,
          100% {
            background-position: -200% 0;
          }
          50% {
            background-position: 200% 0;
          }
        }
      `}</style>
    </>
  );
};

export default ProgressScrollbar;
