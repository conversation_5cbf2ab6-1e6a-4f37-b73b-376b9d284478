/**
 * @file lib/utils.ts
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 * @contributors
 *
 * @description
 * Utility functions for the Velocita Optimized Wiki.
 * Provides essential helpers for className merging, type checking,
 * and common operations used throughout the application.
 *
 * @since 1.0.0
 */

// --------- IMPORTS
import format from '@dynamic-innovative-studio/friendly-dates';

// --------- TYPE DEFINITIONS
type ClassValue =
  | string
  | number
  | boolean
  | undefined
  | null
  | { [key: string]: boolean | undefined | null }
  | ClassValue[];

// --------- UTILITY FUNCTIONS

/**
 * Merges multiple className values into a single string
 * Handles conditional classes, objects, and arrays
 *
 * @param inputs - Array of class values to merge
 * @returns Merged className string
 *
 * @example
 * cn('base-class', condition && 'conditional-class', { 'active': isActive })
 * // Returns: "base-class conditional-class active"
 */
export function cn(...inputs: ClassValue[]): string {
  const classes: string[] = [];

  for (const input of inputs) {
    if (!input) continue;

    if (typeof input === 'string' || typeof input === 'number') {
      classes.push(String(input));
    } else if (typeof input === 'object' && !Array.isArray(input)) {
      // Handle object notation: { 'class-name': condition }
      for (const [key, value] of Object.entries(input)) {
        if (value) {
          classes.push(key);
        }
      }
    } else if (Array.isArray(input)) {
      // Recursively handle arrays
      const nested = cn(...input);
      if (nested) {
        classes.push(nested);
      }
    }
  }

  return classes.join(' ');
}

/**
 * Debounces a function call, ensuring it's only executed after a delay
 * Useful for search inputs, resize handlers, etc.
 *
 * @param func - Function to debounce
 * @param delay - Delay in milliseconds
 * @returns Debounced function
 *
 * @example
 * const debouncedSearch = debounce((query: string) => {
 *   performSearch(query);
 * }, 300);
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * Throttles a function call, ensuring it's only executed at most once per interval
 * Useful for scroll handlers, resize events, etc.
 *
 * @param func - Function to throttle
 * @param limit - Time limit in milliseconds
 * @returns Throttled function
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Generates a random ID string
 * Useful for component keys, unique identifiers, etc.
 *
 * @param length - Length of the generated ID
 * @returns Random ID string
 */
export function generateId(length: number = 8): string {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }

  return result;
}

/**
 * Clamps a number between min and max values
 *
 * @param value - Value to clamp
 * @param min - Minimum value
 * @param max - Maximum value
 * @returns Clamped value
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

/**
 * Formats a number with proper locale formatting
 *
 * @param num - Number to format
 * @param options - Intl.NumberFormat options
 * @returns Formatted number string
 */
export function formatNumber(
  num: number,
  options: Intl.NumberFormatOptions = {}
): string {
  return new Intl.NumberFormat('en-US', options).format(num);
}

/**
 * Safely parses JSON with error handling
 *
 * @param jsonString - JSON string to parse
 * @param fallback - Fallback value if parsing fails
 * @returns Parsed object or fallback
 */
export function safeJsonParse<T>(jsonString: string, fallback: T): T {
  try {
    return JSON.parse(jsonString) as T;
  } catch {
    return fallback;
  }
}

/**
 * Checks if a value is empty (null, undefined, empty string, empty array, empty object)
 *
 * @param value - Value to check
 * @returns True if empty, false otherwise
 */
export function isEmpty(value: any): boolean {
  if (value == null) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
}

/**
 * Creates a deep copy of an object
 *
 * @param obj - Object to clone
 * @returns Deep copy of the object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as T;
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as T;
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  return obj;
}

/**
 * Capitalizes the first letter of a string
 *
 * @param str - String to capitalize
 * @returns Capitalized string
 */
export function capitalize(str: string): string {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Converts a string to kebab-case
 *
 * @param str - String to convert
 * @returns Kebab-case string
 */
export function toKebabCase(str: string): string {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
}

/**
 * Converts a string to camelCase
 *
 * @param str - String to convert
 * @returns CamelCase string
 */
export function toCamelCase(str: string): string {
  return str
    .replace(/[-_\s]+(.)?/g, (_, char) => (char ? char.toUpperCase() : ''))
    .replace(/^[A-Z]/, char => char.toLowerCase());
}

/**
 * Formats a date in a human-readable format using friendly-date
 *
 * @param date - Date to format
 * @param referenceDate - Reference date for relative formatting (optional)
 * @param options - Formatting options
 * @returns Formatted date string
 */
export function formatDate(
  date: Date | string | number,
  referenceDate?: Date | string | number,
  options?: import('@dynamic-innovative-studio/friendly-dates').FormatOptions
): string {
  return format(date, referenceDate, options);
}

/**
 * Calculates the relative time from now using friendly-date
 *
 * @param date - Date to compare
 * @param options - Formatting options (see friendly-date docs)
 * @returns Relative time string (e.g., "2 hours ago")
 */
export function getRelativeTime(
  date: Date | string | number,
  options?: import('@dynamic-innovative-studio/friendly-dates').FormatOptions
): string {
  return format(date, new Date(), options);
}
