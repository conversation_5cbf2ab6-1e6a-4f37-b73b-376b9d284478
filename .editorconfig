# file: .editorconfig
#
# version: 1.0.0
# author: BleckWolf25
# license: MIT
# contributors:
#
# description: This is a standardized EditorConfig file for a JavaScript/TypeScript project.
#
# since 1.0.0

# Top-most EditorConfig file
root = true

# Default settings for all files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript, TypeScript, JSX, and TSX files
[*.{js,jsx,ts,tsx,mjs,cjs}]
indent_style = space
indent_size = 2
max_line_length = 80

# JSON files
[*.json]
indent_style = space
indent_size = 2
max_line_length = 120

# CSS, SCSS, and LESS files
[*.{css,scss,sass,less}]
indent_style = space
indent_size = 2
max_line_length = 120

# HTML files
[*.{html,htm}]
indent_style = space
indent_size = 2
max_line_length = 120

# Markdown files
[*.{md,mdx}]
indent_style = space
indent_size = 2
max_line_length = 100
trim_trailing_whitespace = false

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Package.json and similar files
[package.json]
indent_style = space
indent_size = 2

# Lock files - don't modify formatting
[*.{lock,log}]
insert_final_newline = false
trim_trailing_whitespace = false

# Dockerfile
[Dockerfile*]
indent_style = space
indent_size = 2

# Docker Compose files
[docker-compose*.{yml,yaml}]
indent_style = space
indent_size = 2

# GitHub Actions and workflows
[.github/**/*.{yml,yaml}]
indent_style = space
indent_size = 2
